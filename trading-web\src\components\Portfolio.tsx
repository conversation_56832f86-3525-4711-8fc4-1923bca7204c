'use client'

import { useTradingStore } from '@/lib/store'
import { formatCurrency, formatPercent, formatDateTime } from '@/lib/utils'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'

export default function Portfolio() {
  const { portfolio, setSelectedAsset } = useTradingStore()

  if (!portfolio) {
    return (
      <Card>
        <CardContent className="p-6">
          <p className="text-muted-foreground">Portfolio se načítá...</p>
        </CardContent>
      </Card>
    )
  }

  const handleSelectAsset = async (symbol: string) => {
    // Zde by bylo volání API pro načtení informací o aktivu
    // Pro zjednodušení použijeme mock data
    const mockAsset = {
      symbol,
      name: symbol,
      price: 100,
      change: 0,
      changePercent: 0,
      currency: 'USD',
    }
    setSelectedAsset(mockAsset)
  }

  return (
    <div className="space-y-6">
      {/* Portfolio souhrn */}
      <Card className={`portfolio-card ${portfolio.totalPnL >= 0 ? 'positive' : 'negative'}`}>
        <CardHeader>
          <CardTitle className="text-lg">Portfolio</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <div>
              <p className="text-sm text-muted-foreground">Celková hodnota</p>
              <p className="text-xl font-bold">{formatCurrency(portfolio.totalValue)}</p>
            </div>
            <div>
              <p className="text-sm text-muted-foreground">P&L</p>
              <p className={`text-xl font-bold ${
                portfolio.totalPnL >= 0 ? 'price-up' : 'price-down'
              }`}>
                {formatCurrency(portfolio.totalPnL)}
              </p>
              <p className={`text-sm ${
                portfolio.totalPnL >= 0 ? 'price-up' : 'price-down'
              }`}>
                {formatPercent(portfolio.totalPnLPercent)}
              </p>
            </div>
          </div>

          <div className="pt-4 border-t">
            <div className="flex justify-between items-center">
              <span className="text-sm text-muted-foreground">Hotovost</span>
              <span className="font-medium">{formatCurrency(portfolio.cash)}</span>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Pozice */}
      {portfolio.positions.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Pozice</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {portfolio.positions.map((position) => (
                <div
                  key={position.id}
                  className="border rounded-lg p-3 hover:bg-muted/50 cursor-pointer transition-colors"
                  onClick={() => handleSelectAsset(position.symbol)}
                >
                  <div className="flex justify-between items-start mb-2">
                    <div>
                      <h4 className="font-medium">{position.symbol}</h4>
                      <p className="text-sm text-muted-foreground">
                        {position.quantity} × {formatCurrency(position.currentPrice)}
                      </p>
                    </div>
                    <div className="text-right">
                      <p className="font-medium">{formatCurrency(position.marketValue)}</p>
                      <p className={`text-sm ${
                        position.unrealizedPnL >= 0 ? 'price-up' : 'price-down'
                      }`}>
                        {formatCurrency(position.unrealizedPnL)}
                      </p>
                    </div>
                  </div>
                  
                  <div className="flex justify-between text-xs text-muted-foreground">
                    <span>Vstup: {formatCurrency(position.entryPrice)}</span>
                    <span className={position.unrealizedPnL >= 0 ? 'price-up' : 'price-down'}>
                      {formatPercent(position.unrealizedPnLPercent)}
                    </span>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Historie obchodů */}
      {portfolio.tradeHistory.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Historie obchodů</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2 max-h-60 overflow-y-auto">
              {portfolio.tradeHistory.slice(0, 10).map((trade) => (
                <div
                  key={trade.id}
                  className="flex justify-between items-center py-2 border-b border-border/50 last:border-b-0"
                >
                  <div>
                    <div className="flex items-center space-x-2">
                      <span className={`px-2 py-1 rounded text-xs font-medium ${
                        trade.type === 'buy' 
                          ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
                          : 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'
                      }`}>
                        {trade.type === 'buy' ? 'NÁKUP' : 'PRODEJ'}
                      </span>
                      <span className="font-medium">{trade.symbol}</span>
                    </div>
                    <p className="text-xs text-muted-foreground mt-1">
                      {formatDateTime(trade.timestamp)}
                    </p>
                  </div>
                  
                  <div className="text-right">
                    <p className="font-medium">
                      {trade.quantity} × {formatCurrency(trade.price)}
                    </p>
                    <p className="text-sm text-muted-foreground">
                      {formatCurrency(trade.totalValue)}
                    </p>
                  </div>
                </div>
              ))}
            </div>
            
            {portfolio.tradeHistory.length > 10 && (
              <div className="text-center mt-4">
                <Button variant="outline" size="sm">
                  Zobrazit více
                </Button>
              </div>
            )}
          </CardContent>
        </Card>
      )}

      {/* Prázdné portfolio */}
      {portfolio.positions.length === 0 && portfolio.tradeHistory.length === 0 && (
        <Card>
          <CardContent className="p-6 text-center">
            <p className="text-muted-foreground mb-4">
              Zatím nemáte žádné pozice ani obchody.
            </p>
            <p className="text-sm text-muted-foreground">
              Začněte vyhledáním aktiva a proveďte svůj první obchod!
            </p>
          </CardContent>
        </Card>
      )}
    </div>
  )
}
