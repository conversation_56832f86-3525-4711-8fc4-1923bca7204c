'use client'

import { useState, useEffect } from 'react'
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, BarChart, Bar } from 'recharts'
import { tradingAPI } from '@/lib/api'
import { ChartData } from '@/types/trading'
import { formatCurrency, formatDate } from '@/lib/utils'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'

interface TradingChartProps {
  symbol: string
  period: string
  onPeriodChange: (period: string) => void
}

const PERIODS = [
  { value: '1D', label: '1D' },
  { value: '1W', label: '1T' },
  { value: '1M', label: '1M' },
  { value: '3M', label: '3M' },
  { value: '6M', label: '6M' },
  { value: '1Y', label: '1R' },
]

export default function TradingChart({ symbol, period, onPeriodChange }: TradingChartProps) {
  const [chartData, setChartData] = useState<ChartData[]>([])
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [chartType, setChartType] = useState<'price' | 'volume'>('price')

  useEffect(() => {
    const loadChartData = async () => {
      setIsLoading(true)
      setError(null)
      
      try {
        const response = await tradingAPI.getHistoricalData(symbol, period)
        if (response.success && response.data) {
          setChartData(response.data)
        } else {
          setError(response.error || 'Nepodařilo se načíst data grafu')
        }
      } catch (error) {
        setError('Chyba při načítání dat grafu')
      } finally {
        setIsLoading(false)
      }
    }

    loadChartData()
  }, [symbol, period])

  // Příprava dat pro graf
  const formattedData = chartData.map((item) => ({
    ...item,
    date: formatDate(item.timestamp),
    dateTime: new Date(item.timestamp).getTime(),
  }))

  // Custom tooltip
  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      const data = payload[0].payload
      return (
        <div className="bg-card border rounded-lg p-3 shadow-lg">
          <p className="font-medium">{label}</p>
          <div className="space-y-1 mt-2">
            <p className="text-sm">
              <span className="text-muted-foreground">Otevření: </span>
              <span className="font-medium">{formatCurrency(data.open)}</span>
            </p>
            <p className="text-sm">
              <span className="text-muted-foreground">Maximum: </span>
              <span className="font-medium">{formatCurrency(data.high)}</span>
            </p>
            <p className="text-sm">
              <span className="text-muted-foreground">Minimum: </span>
              <span className="font-medium">{formatCurrency(data.low)}</span>
            </p>
            <p className="text-sm">
              <span className="text-muted-foreground">Zavření: </span>
              <span className="font-medium">{formatCurrency(data.close)}</span>
            </p>
            <p className="text-sm">
              <span className="text-muted-foreground">Objem: </span>
              <span className="font-medium">{data.volume.toLocaleString()}</span>
            </p>
          </div>
        </div>
      )
    }
    return null
  }

  return (
    <Card className="trading-card">
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="text-lg">Graf - {symbol}</CardTitle>
          
          <div className="flex items-center space-x-2">
            {/* Chart type toggle */}
            <div className="flex rounded-md border">
              <Button
                variant={chartType === 'price' ? 'default' : 'ghost'}
                size="sm"
                onClick={() => setChartType('price')}
                className="rounded-r-none"
              >
                Cena
              </Button>
              <Button
                variant={chartType === 'volume' ? 'default' : 'ghost'}
                size="sm"
                onClick={() => setChartType('volume')}
                className="rounded-l-none"
              >
                Objem
              </Button>
            </div>
          </div>
        </div>

        {/* Period selector */}
        <div className="flex space-x-1">
          {PERIODS.map((p) => (
            <Button
              key={p.value}
              variant={period === p.value ? 'default' : 'outline'}
              size="sm"
              onClick={() => onPeriodChange(p.value)}
            >
              {p.label}
            </Button>
          ))}
        </div>
      </CardHeader>

      <CardContent>
        <div className="chart-container">
          {isLoading ? (
            <div className="flex items-center justify-center h-full">
              <div className="text-center">
                <div className="loading-pulse w-16 h-16 mx-auto mb-4"></div>
                <p className="text-muted-foreground">Načítání grafu...</p>
              </div>
            </div>
          ) : error ? (
            <div className="flex items-center justify-center h-full">
              <div className="text-center">
                <p className="text-destructive mb-2">{error}</p>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => window.location.reload()}
                >
                  Zkusit znovu
                </Button>
              </div>
            </div>
          ) : formattedData.length > 0 ? (
            <ResponsiveContainer width="100%" height="100%">
              {chartType === 'price' ? (
                <LineChart data={formattedData}>
                  <CartesianGrid strokeDasharray="3 3" className="opacity-30" />
                  <XAxis 
                    dataKey="date" 
                    tick={{ fontSize: 12 }}
                    tickLine={false}
                  />
                  <YAxis 
                    domain={['dataMin - 10', 'dataMax + 10']}
                    tick={{ fontSize: 12 }}
                    tickLine={false}
                    tickFormatter={(value) => formatCurrency(value).replace(/\s/g, '')}
                  />
                  <Tooltip content={<CustomTooltip />} />
                  <Line
                    type="monotone"
                    dataKey="close"
                    stroke="hsl(var(--primary))"
                    strokeWidth={2}
                    dot={false}
                    activeDot={{ r: 4, stroke: 'hsl(var(--primary))', strokeWidth: 2 }}
                  />
                </LineChart>
              ) : (
                <BarChart data={formattedData}>
                  <CartesianGrid strokeDasharray="3 3" className="opacity-30" />
                  <XAxis 
                    dataKey="date" 
                    tick={{ fontSize: 12 }}
                    tickLine={false}
                  />
                  <YAxis 
                    tick={{ fontSize: 12 }}
                    tickLine={false}
                    tickFormatter={(value) => (value / 1000000).toFixed(1) + 'M'}
                  />
                  <Tooltip 
                    formatter={(value: number) => [value.toLocaleString(), 'Objem']}
                    labelFormatter={(label) => `Datum: ${label}`}
                  />
                  <Bar
                    dataKey="volume"
                    fill="hsl(var(--primary))"
                    opacity={0.7}
                  />
                </BarChart>
              )}
            </ResponsiveContainer>
          ) : (
            <div className="flex items-center justify-center h-full">
              <p className="text-muted-foreground">Žádná data k zobrazení</p>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  )
}
