'use client'

import { useState, useEffect } from 'react'
import { tradingAPI } from '@/lib/api'
import { POPULAR_SYMBOLS } from '@/types/trading'
import { formatCurrency, formatPercent, getPriceChangeColor } from '@/lib/utils'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'

interface MarketItem {
  symbol: string
  name: string
  price: number
  change: number
  changePercent: number
}

export default function MarketOverview() {
  const [marketData, setMarketData] = useState<MarketItem[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [selectedCategory, setSelectedCategory] = useState<'stocks' | 'crypto'>('stocks')

  useEffect(() => {
    const loadMarketData = async () => {
      setIsLoading(true)
      try {
        const symbols = selectedCategory === 'stocks' 
          ? POPULAR_SYMBOLS.stocks.slice(0, 6)
          : POPULAR_SYMBOLS.crypto.slice(0, 6)

        const data: MarketItem[] = []
        
        for (const item of symbols) {
          try {
            const response = await tradingAPI.getAssetInfo(item.symbol)
            if (response.success && response.data) {
              data.push({
                symbol: response.data.symbol,
                name: response.data.name,
                price: response.data.price,
                change: response.data.change,
                changePercent: response.data.changePercent,
              })
            }
          } catch (error) {
            console.error(`Chyba při načítání ${item.symbol}:`, error)
          }
        }

        setMarketData(data)
      } catch (error) {
        console.error('Chyba při načítání tržních dat:', error)
      } finally {
        setIsLoading(false)
      }
    }

    loadMarketData()
  }, [selectedCategory])

  // Aktualizace každých 60 sekund
  useEffect(() => {
    const interval = setInterval(() => {
      if (!isLoading) {
        // Reload data
        const loadMarketData = async () => {
          try {
            const symbols = selectedCategory === 'stocks' 
              ? POPULAR_SYMBOLS.stocks.slice(0, 6)
              : POPULAR_SYMBOLS.crypto.slice(0, 6)

            const data: MarketItem[] = []
            
            for (const item of symbols) {
              try {
                const response = await tradingAPI.getAssetInfo(item.symbol)
                if (response.success && response.data) {
                  data.push({
                    symbol: response.data.symbol,
                    name: response.data.name,
                    price: response.data.price,
                    change: response.data.change,
                    changePercent: response.data.changePercent,
                  })
                }
              } catch (error) {
                console.error(`Chyba při načítání ${item.symbol}:`, error)
              }
            }

            setMarketData(data)
          } catch (error) {
            console.error('Chyba při aktualizaci tržních dat:', error)
          }
        }

        loadMarketData()
      }
    }, 60000)

    return () => clearInterval(interval)
  }, [selectedCategory, isLoading])

  return (
    <Card>
      <CardHeader>
        <div className="flex justify-between items-center">
          <CardTitle className="text-lg">Přehled trhu</CardTitle>
          <div className="flex rounded-md border">
            <button
              onClick={() => setSelectedCategory('stocks')}
              className={`px-3 py-1 text-xs rounded-l-md transition-colors ${
                selectedCategory === 'stocks'
                  ? 'bg-primary text-primary-foreground'
                  : 'hover:bg-muted'
              }`}
            >
              Akcie
            </button>
            <button
              onClick={() => setSelectedCategory('crypto')}
              className={`px-3 py-1 text-xs rounded-r-md transition-colors ${
                selectedCategory === 'crypto'
                  ? 'bg-primary text-primary-foreground'
                  : 'hover:bg-muted'
              }`}
            >
              Krypto
            </button>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        {isLoading ? (
          <div className="space-y-3">
            {[1, 2, 3, 4, 5, 6].map((i) => (
              <div key={i} className="flex justify-between items-center">
                <div className="space-y-1">
                  <div className="loading-pulse h-4 w-16"></div>
                  <div className="loading-pulse h-3 w-24"></div>
                </div>
                <div className="space-y-1 text-right">
                  <div className="loading-pulse h-4 w-20"></div>
                  <div className="loading-pulse h-3 w-16"></div>
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div className="space-y-3">
            {marketData.map((item) => (
              <div
                key={item.symbol}
                className="flex justify-between items-center py-2 hover:bg-muted/50 rounded-lg px-2 -mx-2 cursor-pointer transition-colors"
              >
                <div>
                  <h4 className="font-medium text-sm">{item.symbol}</h4>
                  <p className="text-xs text-muted-foreground truncate max-w-[120px]">
                    {item.name}
                  </p>
                </div>
                
                <div className="text-right">
                  <p className="font-medium text-sm">
                    {formatCurrency(item.price)}
                  </p>
                  <div className={`text-xs ${getPriceChangeColor(item.change)}`}>
                    {item.change >= 0 ? '+' : ''}{formatCurrency(item.change)}
                    <span className="ml-1">
                      ({item.changePercent >= 0 ? '+' : ''}{formatPercent(item.changePercent)})
                    </span>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}

        {/* Market status */}
        <div className="mt-4 pt-4 border-t">
          <div className="flex items-center justify-between text-xs">
            <span className="text-muted-foreground">Stav trhu:</span>
            <div className="market-status open">
              <div className="w-2 h-2 bg-green-500 rounded-full"></div>
              <span>Otevřen</span>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
