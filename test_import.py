#!/usr/bin/env python3

print("Testing imports...")

try:
    import yfinance as yf
    print("✅ yfinance imported successfully")
except ImportError as e:
    print(f"❌ yfinance import failed: {e}")

try:
    import pandas as pd
    print("✅ pandas imported successfully")
except ImportError as e:
    print(f"❌ pandas import failed: {e}")

try:
    import matplotlib.pyplot as plt
    print("✅ matplotlib imported successfully")
except ImportError as e:
    print(f"❌ matplotlib import failed: {e}")

try:
    import numpy as np
    print("✅ numpy imported successfully")
except ImportError as e:
    print(f"❌ numpy import failed: {e}")

print("\nTesting yfinance functionality...")
try:
    ticker = yf.Ticker("AAPL")
    info = ticker.info
    print(f"✅ yfinance works! Apple stock info loaded: {info.get('longName', 'N/A')}")
except Exception as e:
    print(f"❌ yfinance functionality failed: {e}")

print("\nAll tests completed!")
