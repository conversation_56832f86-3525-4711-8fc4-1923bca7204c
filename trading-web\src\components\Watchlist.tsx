'use client'

import { useState, useEffect } from 'react'
import { useTradingStore } from '@/lib/store'
import { tradingAPI } from '@/lib/api'
import { formatCurrency, formatPercent, getPriceChangeColor } from '@/lib/utils'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'

export default function Watchlist() {
  const { watchlist, removeFromWatchlist, setSelectedAsset } = useTradingStore()
  const [isUpdating, setIsUpdating] = useState(false)

  // Aktualizace cen ve watchlistu každých 30 sekund
  useEffect(() => {
    const updateWatchlistPrices = async () => {
      if (watchlist.length === 0) return

      setIsUpdating(true)
      try {
        const symbols = watchlist.map(item => item.symbol)
        const response = await tradingAPI.getMultiplePrices(symbols)
        
        if (response.success && response.data) {
          // Zde by se aktualizovaly ceny ve watchlistu
          // Pro zjednodušení necháme původn<PERSON> ceny
        }
      } catch (error) {
        console.error('Chyba při aktualizaci watchlistu:', error)
      } finally {
        setIsUpdating(false)
      }
    }

    // Okamžitá aktualizace
    updateWatchlistPrices()

    // Pravidelná aktualizace každých 30 sekund
    const interval = setInterval(updateWatchlistPrices, 30000)

    return () => clearInterval(interval)
  }, [watchlist])

  const handleSelectAsset = async (symbol: string) => {
    try {
      const response = await tradingAPI.getAssetInfo(symbol)
      if (response.success && response.data) {
        setSelectedAsset(response.data)
      }
    } catch (error) {
      console.error('Chyba při načítání aktiva:', error)
    }
  }

  if (watchlist.length === 0) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Watchlist</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-6">
            <p className="text-muted-foreground mb-2">
              Váš watchlist je prázdný
            </p>
            <p className="text-sm text-muted-foreground">
              Přidejte aktiva kliknutím na "+" u populárních symbolů
            </p>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex justify-between items-center">
          <CardTitle className="text-lg">Watchlist</CardTitle>
          {isUpdating && (
            <div className="w-4 h-4 border-2 border-primary border-t-transparent rounded-full animate-spin" />
          )}
        </div>
      </CardHeader>
      <CardContent>
        <div className="space-y-2">
          {watchlist.map((item) => (
            <div
              key={item.symbol}
              className="flex items-center justify-between p-3 border rounded-lg hover:bg-muted/50 cursor-pointer transition-colors group"
              onClick={() => handleSelectAsset(item.symbol)}
            >
              <div className="flex-1">
                <div className="flex items-center justify-between">
                  <div>
                    <h4 className="font-medium text-sm">{item.symbol}</h4>
                    <p className="text-xs text-muted-foreground truncate">
                      {item.name}
                    </p>
                  </div>
                  
                  <div className="text-right">
                    <p className="font-medium text-sm">
                      {formatCurrency(item.price)}
                    </p>
                    <div className={`text-xs ${getPriceChangeColor(item.change)}`}>
                      {formatCurrency(item.change)} ({formatPercent(item.changePercent)})
                    </div>
                  </div>
                </div>
              </div>

              <Button
                variant="ghost"
                size="sm"
                onClick={(e) => {
                  e.stopPropagation()
                  removeFromWatchlist(item.symbol)
                }}
                className="opacity-0 group-hover:opacity-100 transition-opacity ml-2 p-1 h-6 w-6"
                title="Odebrat z watchlistu"
              >
                ×
              </Button>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  )
}
