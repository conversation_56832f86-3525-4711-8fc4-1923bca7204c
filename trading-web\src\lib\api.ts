import { Asset, ChartData, MarketData, ApiResponse } from '@/types/trading';

// Simulace API - v re<PERSON><PERSON><PERSON> by to bylo p<PERSON><PERSON> k <PERSON> Finance, Alpha Vantage, atd.
class TradingAPI {
  private baseUrl = '/api';
  private cache = new Map<string, { data: any; timestamp: number }>();
  private cacheTimeout = 30000; // 30 sekund

  // Simulovaná data pro demo
  private mockPrices: Record<string, number> = {
    'AAPL': 175.50,
    'GOOGL': 2850.00,
    'MSFT': 415.25,
    'TSLA': 245.80,
    'AMZN': 3420.00,
    'NVDA': 875.30,
    'META': 485.60,
    'NFLX': 425.90,
    'BTC-USD': 105000.00,
    'ETH-USD': 3850.00,
    'ADA-USD': 0.85,
    'DOT-USD': 12.50,
    'SOL-USD': 185.00,
    'MATIC-USD': 1.25,
    'EURUSD=X': 1.0850,
    'GBPUSD=X': 1.2650,
    'USDJPY=X': 148.50,
    'USDCHF=X': 0.8750,
    'GC=F': 2050.00,
    'SI=F': 24.50,
    'CL=F': 78.50,
    'NG=F': 3.25,
  };

  private getFromCache<T>(key: string): T | null {
    const cached = this.cache.get(key);
    if (cached && Date.now() - cached.timestamp < this.cacheTimeout) {
      return cached.data as T;
    }
    return null;
  }

  private setCache(key: string, data: any): void {
    this.cache.set(key, { data, timestamp: Date.now() });
  }

  // Simulace volatility
  private simulatePrice(basePrice: number): number {
    const volatility = 0.02; // 2% volatilita
    const change = (Math.random() - 0.5) * 2 * volatility;
    return basePrice * (1 + change);
  }

  async getCurrentPrice(symbol: string): Promise<ApiResponse<number>> {
    try {
      // Simulace API delay
      await new Promise(resolve => setTimeout(resolve, 100 + Math.random() * 200));

      const basePrice = this.mockPrices[symbol];
      if (!basePrice) {
        return { success: false, error: `Symbol ${symbol} not found` };
      }

      const currentPrice = this.simulatePrice(basePrice);
      
      return { success: true, data: currentPrice };
    } catch (error) {
      return { success: false, error: 'Failed to fetch price' };
    }
  }

  async getAssetInfo(symbol: string): Promise<ApiResponse<Asset>> {
    try {
      const cached = this.getFromCache<Asset>(`asset-${symbol}`);
      if (cached) return { success: true, data: cached };

      // Simulace API delay
      await new Promise(resolve => setTimeout(resolve, 200 + Math.random() * 300));

      const basePrice = this.mockPrices[symbol];
      if (!basePrice) {
        return { success: false, error: `Symbol ${symbol} not found` };
      }

      const currentPrice = this.simulatePrice(basePrice);
      const previousPrice = basePrice;
      const change = currentPrice - previousPrice;
      const changePercent = (change / previousPrice) * 100;

      const asset: Asset = {
        symbol,
        name: this.getAssetName(symbol),
        price: currentPrice,
        change,
        changePercent,
        volume: Math.floor(Math.random() * 10000000) + 1000000,
        currency: symbol.includes('USD') ? 'USD' : 'USD',
        sector: this.getAssetSector(symbol),
        industry: this.getAssetIndustry(symbol),
      };

      this.setCache(`asset-${symbol}`, asset);
      return { success: true, data: asset };
    } catch (error) {
      return { success: false, error: 'Failed to fetch asset info' };
    }
  }

  async getHistoricalData(symbol: string, period: string = '1M'): Promise<ApiResponse<ChartData[]>> {
    try {
      const cached = this.getFromCache<ChartData[]>(`chart-${symbol}-${period}`);
      if (cached) return { success: true, data: cached };

      // Simulace API delay
      await new Promise(resolve => setTimeout(resolve, 500 + Math.random() * 500));

      const basePrice = this.mockPrices[symbol];
      if (!basePrice) {
        return { success: false, error: `Symbol ${symbol} not found` };
      }

      const data = this.generateHistoricalData(basePrice, period);
      
      this.setCache(`chart-${symbol}-${period}`, data);
      return { success: true, data };
    } catch (error) {
      return { success: false, error: 'Failed to fetch historical data' };
    }
  }

  async getMultiplePrices(symbols: string[]): Promise<ApiResponse<Record<string, number>>> {
    try {
      // Simulace API delay
      await new Promise(resolve => setTimeout(resolve, 150 + Math.random() * 250));

      const prices: Record<string, number> = {};
      
      for (const symbol of symbols) {
        const basePrice = this.mockPrices[symbol];
        if (basePrice) {
          prices[symbol] = this.simulatePrice(basePrice);
        }
      }

      return { success: true, data: prices };
    } catch (error) {
      return { success: false, error: 'Failed to fetch prices' };
    }
  }

  private generateHistoricalData(basePrice: number, period: string): ChartData[] {
    const days = this.getPeriodDays(period);
    const data: ChartData[] = [];
    
    let currentPrice = basePrice * 0.9; // Začneme 10% níže
    const dailyVolatility = 0.03; // 3% denní volatilita

    for (let i = days; i >= 0; i--) {
      const date = new Date();
      date.setDate(date.getDate() - i);
      
      const change = (Math.random() - 0.5) * 2 * dailyVolatility;
      const open = currentPrice;
      const close = currentPrice * (1 + change);
      const high = Math.max(open, close) * (1 + Math.random() * 0.02);
      const low = Math.min(open, close) * (1 - Math.random() * 0.02);
      const volume = Math.floor(Math.random() * 5000000) + 1000000;

      data.push({
        timestamp: date.toISOString(),
        open,
        high,
        low,
        close,
        volume,
      });

      currentPrice = close;
    }

    return data;
  }

  private getPeriodDays(period: string): number {
    switch (period) {
      case '1D': return 1;
      case '1W': return 7;
      case '1M': return 30;
      case '3M': return 90;
      case '6M': return 180;
      case '1Y': return 365;
      default: return 30;
    }
  }

  private getAssetName(symbol: string): string {
    const names: Record<string, string> = {
      'AAPL': 'Apple Inc.',
      'GOOGL': 'Alphabet Inc.',
      'MSFT': 'Microsoft Corporation',
      'TSLA': 'Tesla, Inc.',
      'AMZN': 'Amazon.com Inc.',
      'NVDA': 'NVIDIA Corporation',
      'META': 'Meta Platforms Inc.',
      'NFLX': 'Netflix Inc.',
      'BTC-USD': 'Bitcoin',
      'ETH-USD': 'Ethereum',
      'ADA-USD': 'Cardano',
      'DOT-USD': 'Polkadot',
      'SOL-USD': 'Solana',
      'MATIC-USD': 'Polygon',
      'EURUSD=X': 'EUR/USD',
      'GBPUSD=X': 'GBP/USD',
      'USDJPY=X': 'USD/JPY',
      'USDCHF=X': 'USD/CHF',
      'GC=F': 'Gold Futures',
      'SI=F': 'Silver Futures',
      'CL=F': 'Crude Oil Futures',
      'NG=F': 'Natural Gas Futures',
    };
    return names[symbol] || symbol;
  }

  private getAssetSector(symbol: string): string | undefined {
    if (symbol.includes('-USD')) return 'Cryptocurrency';
    if (symbol.includes('=X')) return 'Currency';
    if (symbol.includes('=F')) return 'Commodity';
    return 'Technology'; // Default pro akcie
  }

  private getAssetIndustry(symbol: string): string | undefined {
    const industries: Record<string, string> = {
      'AAPL': 'Consumer Electronics',
      'GOOGL': 'Internet Content & Information',
      'MSFT': 'Software',
      'TSLA': 'Auto Manufacturers',
      'AMZN': 'Internet Retail',
      'NVDA': 'Semiconductors',
      'META': 'Internet Content & Information',
      'NFLX': 'Entertainment',
    };
    return industries[symbol];
  }
}

export const tradingAPI = new TradingAPI();
