import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { 
  TradingState, 
  Portfolio, 
  Asset, 
  MarketData, 
  WatchlistItem,
  Position,
  Trade
} from '@/types/trading';

// Vytvoření výchozího portfolia
const createDefaultPortfolio = (): Portfolio => ({
  id: 'default',
  cash: 10000,
  totalValue: 10000,
  totalPnL: 0,
  totalPnLPercent: 0,
  positions: [],
  tradeHistory: [],
  initialCash: 10000,
});

export const useTradingStore = create<TradingState>()(
  persist(
    (set, get) => ({
      // Initial state
      portfolio: createDefaultPortfolio(),
      selectedAsset: null,
      marketData: {},
      watchlist: [],
      isLoading: false,
      error: null,

      // Actions
      setPortfolio: (portfolio: Portfolio) => 
        set({ portfolio }),

      setSelectedAsset: (asset: Asset | null) => 
        set({ selectedAsset: asset }),

      updateMarketData: (symbol: string, data: MarketData) =>
        set((state) => ({
          marketData: {
            ...state.marketData,
            [symbol]: data,
          },
        })),

      addToWatchlist: (item: WatchlistItem) =>
        set((state) => {
          const exists = state.watchlist.some(w => w.symbol === item.symbol);
          if (exists) return state;
          
          return {
            watchlist: [...state.watchlist, item],
          };
        }),

      removeFromWatchlist: (symbol: string) =>
        set((state) => ({
          watchlist: state.watchlist.filter(item => item.symbol !== symbol),
        })),

      setLoading: (loading: boolean) => 
        set({ isLoading: loading }),

      setError: (error: string | null) => 
        set({ error }),

      // Portfolio management actions
      executeTrade: (trade: Omit<Trade, 'id' | 'timestamp' | 'status'>) => {
        const state = get();
        if (!state.portfolio) return;

        const newTrade: Trade = {
          ...trade,
          id: Date.now().toString(),
          timestamp: new Date().toISOString(),
          status: 'completed',
        };

        const updatedPortfolio = { ...state.portfolio };
        
        if (trade.type === 'buy') {
          // Kontrola dostupné hotovosti
          if (updatedPortfolio.cash < trade.totalValue) {
            set({ error: 'Nedostatek hotovosti pro nákup' });
            return;
          }
          
          // Odečtení hotovosti
          updatedPortfolio.cash -= trade.totalValue;
          
          // Přidání nebo aktualizace pozice
          const existingPositionIndex = updatedPortfolio.positions.findIndex(
            p => p.symbol === trade.symbol
          );
          
          if (existingPositionIndex >= 0) {
            // Aktualizace existující pozice
            const existingPosition = updatedPortfolio.positions[existingPositionIndex];
            const totalQuantity = existingPosition.quantity + trade.quantity;
            const totalValue = (existingPosition.quantity * existingPosition.entryPrice) + trade.totalValue;
            const avgPrice = totalValue / totalQuantity;
            
            updatedPortfolio.positions[existingPositionIndex] = {
              ...existingPosition,
              quantity: totalQuantity,
              entryPrice: avgPrice,
              currentPrice: trade.price,
              marketValue: totalQuantity * trade.price,
              unrealizedPnL: totalQuantity * (trade.price - avgPrice),
              unrealizedPnLPercent: ((trade.price - avgPrice) / avgPrice) * 100,
            };
          } else {
            // Nová pozice
            const newPosition: Position = {
              id: Date.now().toString(),
              symbol: trade.symbol,
              quantity: trade.quantity,
              entryPrice: trade.price,
              currentPrice: trade.price,
              entryTime: new Date().toISOString(),
              marketValue: trade.totalValue,
              unrealizedPnL: 0,
              unrealizedPnLPercent: 0,
              type: 'long',
            };
            updatedPortfolio.positions.push(newPosition);
          }
        } else {
          // Prodej
          const positionIndex = updatedPortfolio.positions.findIndex(
            p => p.symbol === trade.symbol
          );
          
          if (positionIndex === -1) {
            set({ error: 'Nemáte pozici v tomto aktivu' });
            return;
          }
          
          const position = updatedPortfolio.positions[positionIndex];
          
          if (position.quantity < trade.quantity) {
            set({ error: 'Nedostatek akcií pro prodej' });
            return;
          }
          
          // Přidání hotovosti
          updatedPortfolio.cash += trade.totalValue;
          
          // Aktualizace pozice
          if (position.quantity === trade.quantity) {
            // Uzavření pozice
            updatedPortfolio.positions.splice(positionIndex, 1);
          } else {
            // Částečný prodej
            updatedPortfolio.positions[positionIndex] = {
              ...position,
              quantity: position.quantity - trade.quantity,
              currentPrice: trade.price,
              marketValue: (position.quantity - trade.quantity) * trade.price,
              unrealizedPnL: (position.quantity - trade.quantity) * (trade.price - position.entryPrice),
              unrealizedPnLPercent: ((trade.price - position.entryPrice) / position.entryPrice) * 100,
            };
          }
        }
        
        // Přidání obchodu do historie
        updatedPortfolio.tradeHistory.unshift(newTrade);
        
        // Přepočítání celkové hodnoty portfolia
        const totalPositionsValue = updatedPortfolio.positions.reduce(
          (sum, pos) => sum + pos.marketValue, 0
        );
        updatedPortfolio.totalValue = updatedPortfolio.cash + totalPositionsValue;
        updatedPortfolio.totalPnL = updatedPortfolio.totalValue - updatedPortfolio.initialCash;
        updatedPortfolio.totalPnLPercent = (updatedPortfolio.totalPnL / updatedPortfolio.initialCash) * 100;
        
        set({ portfolio: updatedPortfolio, error: null });
      },

      // Aktualizace cen pozic
      updatePositionPrices: (prices: Record<string, number>) => {
        const state = get();
        if (!state.portfolio) return;

        const updatedPortfolio = { ...state.portfolio };
        
        updatedPortfolio.positions = updatedPortfolio.positions.map(position => {
          const currentPrice = prices[position.symbol];
          if (!currentPrice) return position;

          const marketValue = position.quantity * currentPrice;
          const unrealizedPnL = position.quantity * (currentPrice - position.entryPrice);
          const unrealizedPnLPercent = ((currentPrice - position.entryPrice) / position.entryPrice) * 100;

          return {
            ...position,
            currentPrice,
            marketValue,
            unrealizedPnL,
            unrealizedPnLPercent,
          };
        });

        // Přepočítání celkové hodnoty
        const totalPositionsValue = updatedPortfolio.positions.reduce(
          (sum, pos) => sum + pos.marketValue, 0
        );
        updatedPortfolio.totalValue = updatedPortfolio.cash + totalPositionsValue;
        updatedPortfolio.totalPnL = updatedPortfolio.totalValue - updatedPortfolio.initialCash;
        updatedPortfolio.totalPnLPercent = (updatedPortfolio.totalPnL / updatedPortfolio.initialCash) * 100;

        set({ portfolio: updatedPortfolio });
      },

      // Reset portfolia
      resetPortfolio: () => {
        set({ portfolio: createDefaultPortfolio() });
      },
    }),
    {
      name: 'trading-store',
      partialize: (state) => ({
        portfolio: state.portfolio,
        watchlist: state.watchlist,
      }),
    }
  )
);

// Rozšíření TradingState interface
interface ExtendedTradingState extends TradingState {
  executeTrade: (trade: Omit<Trade, 'id' | 'timestamp' | 'status'>) => void;
  updatePositionPrices: (prices: Record<string, number>) => void;
  resetPortfolio: () => void;
}
