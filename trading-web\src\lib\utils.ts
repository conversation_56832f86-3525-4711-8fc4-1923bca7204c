import { type ClassValue, clsx } from "clsx"
import { twMerge } from "tailwind-merge"

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

// Formátování čísel
export function formatCurrency(value: number, currency: string = 'USD'): string {
  return new Intl.NumberFormat('cs-CZ', {
    style: 'currency',
    currency: currency,
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
  }).format(value)
}

export function formatNumber(value: number, decimals: number = 2): string {
  return new Intl.NumberFormat('cs-CZ', {
    minimumFractionDigits: decimals,
    maximumFractionDigits: decimals,
  }).format(value)
}

export function formatPercent(value: number, decimals: number = 2): string {
  return new Intl.NumberFormat('cs-CZ', {
    style: 'percent',
    minimumFractionDigits: decimals,
    maximumFractionDigits: decimals,
  }).format(value / 100)
}

export function formatCompactNumber(value: number): string {
  return new Intl.NumberFormat('cs-CZ', {
    notation: 'compact',
    maximumFractionDigits: 1,
  }).format(value)
}

// Formátování datumu a času
export function formatDate(date: string | Date): string {
  const d = typeof date === 'string' ? new Date(date) : date
  return new Intl.DateTimeFormat('cs-CZ', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
  }).format(d)
}

export function formatDateTime(date: string | Date): string {
  const d = typeof date === 'string' ? new Date(date) : date
  return new Intl.DateTimeFormat('cs-CZ', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
  }).format(d)
}

export function formatTime(date: string | Date): string {
  const d = typeof date === 'string' ? new Date(date) : date
  return new Intl.DateTimeFormat('cs-CZ', {
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit',
  }).format(d)
}

// Relativní čas
export function formatRelativeTime(date: string | Date): string {
  const d = typeof date === 'string' ? new Date(date) : date
  const now = new Date()
  const diffInSeconds = Math.floor((now.getTime() - d.getTime()) / 1000)

  if (diffInSeconds < 60) {
    return 'právě teď'
  } else if (diffInSeconds < 3600) {
    const minutes = Math.floor(diffInSeconds / 60)
    return `před ${minutes} min`
  } else if (diffInSeconds < 86400) {
    const hours = Math.floor(diffInSeconds / 3600)
    return `před ${hours} h`
  } else {
    const days = Math.floor(diffInSeconds / 86400)
    return `před ${days} dny`
  }
}

// Barvy pro změny cen
export function getPriceChangeColor(change: number): string {
  if (change > 0) return 'text-green-600 dark:text-green-400'
  if (change < 0) return 'text-red-600 dark:text-red-400'
  return 'text-gray-600 dark:text-gray-400'
}

export function getPriceChangeIcon(change: number): string {
  if (change > 0) return '↗'
  if (change < 0) return '↘'
  return '→'
}

// Validace symbolů
export function isValidSymbol(symbol: string): boolean {
  // Základní validace symbolu
  return /^[A-Z0-9\-=\.]+$/.test(symbol) && symbol.length >= 1 && symbol.length <= 10
}

// Generování ID
export function generateId(): string {
  return Date.now().toString(36) + Math.random().toString(36).substr(2)
}

// Debounce funkce
export function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout | null = null
  
  return (...args: Parameters<T>) => {
    if (timeout) clearTimeout(timeout)
    timeout = setTimeout(() => func(...args), wait)
  }
}

// Throttle funkce
export function throttle<T extends (...args: any[]) => any>(
  func: T,
  limit: number
): (...args: Parameters<T>) => void {
  let inThrottle: boolean
  
  return (...args: Parameters<T>) => {
    if (!inThrottle) {
      func(...args)
      inThrottle = true
      setTimeout(() => inThrottle = false, limit)
    }
  }
}

// Kalkulace pro trading
export function calculatePnL(entryPrice: number, currentPrice: number, quantity: number): {
  pnl: number
  pnlPercent: number
} {
  const pnl = (currentPrice - entryPrice) * quantity
  const pnlPercent = ((currentPrice - entryPrice) / entryPrice) * 100
  
  return { pnl, pnlPercent }
}

export function calculateMarketValue(price: number, quantity: number): number {
  return price * quantity
}

export function calculateTotalCost(price: number, quantity: number, fee: number = 0): number {
  return (price * quantity) + fee
}

// Validace obchodních příkazů
export function validateTradeOrder(
  type: 'buy' | 'sell',
  quantity: number,
  price: number,
  availableCash: number,
  availableShares: number = 0
): { isValid: boolean; error?: string } {
  if (quantity <= 0) {
    return { isValid: false, error: 'Množství musí být větší než 0' }
  }

  if (price <= 0) {
    return { isValid: false, error: 'Cena musí být větší než 0' }
  }

  if (type === 'buy') {
    const totalCost = calculateTotalCost(price, quantity)
    if (totalCost > availableCash) {
      return { isValid: false, error: 'Nedostatek hotovosti' }
    }
  } else if (type === 'sell') {
    if (quantity > availableShares) {
      return { isValid: false, error: 'Nedostatek akcií' }
    }
  }

  return { isValid: true }
}

// Lokální úložiště
export function saveToLocalStorage(key: string, data: any): void {
  try {
    localStorage.setItem(key, JSON.stringify(data))
  } catch (error) {
    console.error('Error saving to localStorage:', error)
  }
}

export function loadFromLocalStorage<T>(key: string, defaultValue: T): T {
  try {
    const item = localStorage.getItem(key)
    return item ? JSON.parse(item) : defaultValue
  } catch (error) {
    console.error('Error loading from localStorage:', error)
    return defaultValue
  }
}

// URL parametry
export function getUrlParams(): URLSearchParams {
  return new URLSearchParams(window.location.search)
}

export function setUrlParam(key: string, value: string): void {
  const url = new URL(window.location.href)
  url.searchParams.set(key, value)
  window.history.replaceState({}, '', url.toString())
}

// Kopírování do schránky
export async function copyToClipboard(text: string): Promise<boolean> {
  try {
    await navigator.clipboard.writeText(text)
    return true
  } catch (error) {
    console.error('Failed to copy to clipboard:', error)
    return false
  }
}
