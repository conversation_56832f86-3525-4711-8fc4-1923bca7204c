# Jednoduchá Trading Aplikace

Tato aplikace umožňuje simulaci obchodování s akciemi, kryptoměnami a dalšími finančními aktivy pomocí reálných tržních dat.

## Funkce

- 📈 **Získávání reálných tržních dat** z Yahoo Finance API
- 📊 **Vykreslování cenových grafů** pomocí matplotlib
- 💰 **Simulace nákupu a prodeje** aktiv
- 📋 **Správa portfolia** s výpočtem zisků/ztrát
- 💾 **Ukládání a načítání** portfolia
- 🔍 **Sledování pozic** a jejich výkonnosti

## Instalace

1. **Naklonujte nebo stáhněte projekt**

2. **Nainstalujte závislosti:**
   ```bash
   pip install -r requirements.txt
   ```

3. **Spusťte aplikaci:**
   ```bash
   python main.py
   ```

## Použití

### Základní workflow:

1. **Nastavte aktivní symbol** (např. AAPL, BTC-USD, TSLA)
2. **Zobrazit aktuální cenu** a cenový graf
3. **Koupit nebo prodat** aktivum
4. **Sledovat portfolio** a jeho výkonnost

### Podporované symboly:

- **Akcie:** AAPL, GOOGL, MSFT, TSLA, AMZN, NVDA
- **Kryptoměny:** BTC-USD, ETH-USD, ADA-USD, DOT-USD
- **Forex:** EURUSD=X, GBPUSD=X, USDJPY=X
- **Komodity:** GC=F (zlato), SI=F (stříbro), CL=F (ropa)

### Menu aplikace:

```
1. Nastavit aktivní symbol
2. Zobrazit aktuální cenu
3. Zobrazit cenový graf
4. Koupit aktivum
5. Prodat aktivum
6. Zobrazit portfolio
7. Uložit portfolio
8. Načíst portfolio
9. Zobrazit populární symboly
0. Ukončit aplikaci
```

## Struktura projektu

```
trade/
├── main.py              # Hlavní soubor aplikace
├── market_data.py       # Modul pro získávání tržních dat
├── trading_engine.py    # Modul pro simulaci obchodů
├── portfolio.py         # Modul pro správu portfolia
├── requirements.txt     # Seznam závislostí
└── README.md           # Tento soubor
```

## Příklady použití

### 1. Nákup akcií Apple:
```
1. Vyberte "1" - Nastavit aktivní symbol
2. Zadejte "AAPL"
3. Vyberte "2" - Zobrazit aktuální cenu
4. Vyberte "4" - Koupit aktivum
5. Zadejte množství (např. 10)
6. Potvrďte nákup
```

### 2. Sledování Bitcoinu:
```
1. Vyberte "1" - Nastavit aktivní symbol
2. Zadejte "BTC-USD"
3. Vyberte "3" - Zobrazit cenový graf
4. Vyberte období (např. 1 měsíc)
```

### 3. Zobrazení portfolia:
```
1. Vyberte "6" - Zobrazit portfolio
2. Uvidíte všechny pozice, P&L a celkovou hodnotu
```

## Rozšíření aplikace

Aplikace je navržena pro snadné rozšiřování. Můžete přidat:

### Stop-Loss a Take-Profit:
```python
# V trading_engine.py
def set_stop_loss(self, symbol: str, stop_price: float):
    # Implementace stop-loss logiky
    pass

def set_take_profit(self, symbol: str, target_price: float):
    # Implementace take-profit logiky
    pass
```

### Technické indikátory:
```python
# V market_data.py
def calculate_sma(self, data: pd.DataFrame, period: int):
    # Jednoduchý klouzavý průměr
    return data['Close'].rolling(window=period).mean()

def calculate_rsi(self, data: pd.DataFrame, period: int = 14):
    # Relative Strength Index
    pass
```

### Alerty a notifikace:
```python
# Nový modul alerts.py
class AlertManager:
    def set_price_alert(self, symbol: str, target_price: float):
        # Nastavení cenového alertu
        pass
```

## Poznámky

- **Simulace:** Všechny obchody jsou pouze simulované, žádné reálné peníze nejsou použity
- **Data:** Aplikace používá reálná tržní data z Yahoo Finance
- **Portfolio:** Automaticky se ukládá při ukončení aplikace
- **Grafy:** Vyžadují GUI prostředí pro zobrazení matplotlib grafů

## Řešení problémů

### Chyba při získávání dat:
- Zkontrolujte internetové připojení
- Ověřte, že symbol je správně zadán
- Některé symboly mohou být dočasně nedostupné

### Chyba při zobrazování grafů:
- Ujistěte se, že máte nainstalovaný matplotlib
- Na některých systémech může být potřeba GUI backend

### Chyba při ukládání portfolia:
- Zkontrolujte oprávnění k zápisu do adresáře
- Ujistěte se, že máte dostatek místa na disku

## Licence

Tento projekt je určen pro vzdělávací účely. Používejte na vlastní odpovědnost.
