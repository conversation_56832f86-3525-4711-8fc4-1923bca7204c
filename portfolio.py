"""
Modul pro správu portfolia a pozic
"""

from typing import Dict, List, Optional
from datetime import datetime
import json


class Position:
    """Třída reprezentující pozici v aktivu"""
    
    def __init__(self, symbol: str, quantity: float, entry_price: float, 
                 entry_time: datetime = None):
        self.symbol = symbol
        self.quantity = quantity  # Kladné = long pozice, záporné = short pozice
        self.entry_price = entry_price
        self.entry_time = entry_time or datetime.now()
    
    def get_market_value(self, current_price: float) -> float:
        """Vypočítá tržní hodnotu pozice"""
        return abs(self.quantity) * current_price
    
    def get_unrealized_pnl(self, current_price: float) -> float:
        """Vypočítá nerealizovaný zisk/ztrátu"""
        if self.quantity > 0:  # Long pozice
            return self.quantity * (current_price - self.entry_price)
        else:  # Short pozice
            return abs(self.quantity) * (self.entry_price - current_price)
    
    def get_unrealized_pnl_percent(self, current_price: float) -> float:
        """Vypočítá nerealizovaný zisk/ztrátu v procentech"""
        if self.entry_price == 0:
            return 0.0
        
        pnl = self.get_unrealized_pnl(current_price)
        invested_amount = abs(self.quantity) * self.entry_price
        
        return (pnl / invested_amount) * 100 if invested_amount > 0 else 0.0
    
    def to_dict(self) -> Dict:
        """Převede pozici na slovník"""
        return {
            'symbol': self.symbol,
            'quantity': self.quantity,
            'entry_price': self.entry_price,
            'entry_time': self.entry_time.isoformat()
        }
    
    @classmethod
    def from_dict(cls, data: Dict) -> 'Position':
        """Vytvoří pozici ze slovníku"""
        return cls(
            symbol=data['symbol'],
            quantity=data['quantity'],
            entry_price=data['entry_price'],
            entry_time=datetime.fromisoformat(data['entry_time'])
        )


class Portfolio:
    """Třída pro správu portfolia"""
    
    def __init__(self, initial_cash: float = 10000.0):
        self.initial_cash = initial_cash
        self.cash = initial_cash
        self.positions: Dict[str, Position] = {}
        self.trade_history: List[Dict] = []
    
    def get_available_cash(self) -> float:
        """Vrátí dostupnou hotovost"""
        return self.cash
    
    def get_position(self, symbol: str) -> Optional[Position]:
        """Vrátí pozici pro daný symbol"""
        return self.positions.get(symbol)
    
    def has_position(self, symbol: str) -> bool:
        """Zkontroluje, zda má portfolio pozici v daném aktivu"""
        return symbol in self.positions
    
    def add_position(self, symbol: str, quantity: float, price: float) -> bool:
        """
        Přidá nebo upraví pozici
        
        Args:
            symbol: Symbol aktiva
            quantity: Množství (kladné = nákup, záporné = prodej)
            price: Cena za jednotku
            
        Returns:
            True pokud byla transakce úspěšná
        """
        total_cost = abs(quantity) * price
        
        # Kontrola dostupné hotovosti pro nákup
        if quantity > 0 and total_cost > self.cash:
            print(f"Nedostatek hotovosti! Potřebujete ${total_cost:.2f}, máte ${self.cash:.2f}")
            return False
        
        # Kontrola dostupného množství pro prodej
        if quantity < 0:
            current_position = self.get_position(symbol)
            if not current_position or current_position.quantity < abs(quantity):
                available = current_position.quantity if current_position else 0
                print(f"Nedostatek akcií pro prodej! Chcete prodat {abs(quantity)}, máte {available}")
                return False
        
        # Provedení transakce
        if symbol in self.positions:
            # Úprava existující pozice
            current_pos = self.positions[symbol]
            new_quantity = current_pos.quantity + quantity
            
            if new_quantity == 0:
                # Uzavření pozice
                realized_pnl = quantity * (price - current_pos.entry_price)
                self.cash += abs(quantity) * price + realized_pnl
                del self.positions[symbol]
            elif new_quantity > 0:
                # Stále long pozice
                if current_pos.quantity > 0:
                    # Průměrování ceny
                    total_value = (current_pos.quantity * current_pos.entry_price + 
                                 quantity * price)
                    new_avg_price = total_value / new_quantity
                    current_pos.quantity = new_quantity
                    current_pos.entry_price = new_avg_price
                else:
                    # Změna ze short na long
                    current_pos.quantity = new_quantity
                    current_pos.entry_price = price
                    current_pos.entry_time = datetime.now()
                
                if quantity > 0:
                    self.cash -= total_cost
                else:
                    self.cash += total_cost
            else:
                # Stále short pozice nebo změna na short
                current_pos.quantity = new_quantity
                if quantity < 0:
                    self.cash += total_cost
        else:
            # Nová pozice
            self.positions[symbol] = Position(symbol, quantity, price)
            if quantity > 0:
                self.cash -= total_cost
            else:
                self.cash += total_cost
        
        # Záznam do historie
        self.trade_history.append({
            'timestamp': datetime.now().isoformat(),
            'symbol': symbol,
            'quantity': quantity,
            'price': price,
            'total_value': total_cost,
            'type': 'BUY' if quantity > 0 else 'SELL'
        })
        
        return True
    
    def get_portfolio_value(self, current_prices: Dict[str, float]) -> float:
        """Vypočítá celkovou hodnotu portfolia"""
        total_value = self.cash
        
        for symbol, position in self.positions.items():
            if symbol in current_prices:
                total_value += position.get_market_value(current_prices[symbol])
        
        return total_value
    
    def get_total_pnl(self, current_prices: Dict[str, float]) -> float:
        """Vypočítá celkový zisk/ztrátu"""
        current_value = self.get_portfolio_value(current_prices)
        return current_value - self.initial_cash
    
    def get_portfolio_summary(self, current_prices: Dict[str, float]) -> Dict:
        """Vrátí souhrn portfolia"""
        total_value = self.get_portfolio_value(current_prices)
        total_pnl = self.get_total_pnl(current_prices)
        
        positions_summary = []
        for symbol, position in self.positions.items():
            if symbol in current_prices:
                current_price = current_prices[symbol]
                positions_summary.append({
                    'symbol': symbol,
                    'quantity': position.quantity,
                    'entry_price': position.entry_price,
                    'current_price': current_price,
                    'market_value': position.get_market_value(current_price),
                    'unrealized_pnl': position.get_unrealized_pnl(current_price),
                    'unrealized_pnl_percent': position.get_unrealized_pnl_percent(current_price)
                })
        
        return {
            'cash': self.cash,
            'total_value': total_value,
            'total_pnl': total_pnl,
            'total_pnl_percent': (total_pnl / self.initial_cash) * 100,
            'positions': positions_summary
        }
    
    def save_to_file(self, filename: str):
        """Uloží portfolio do souboru"""
        data = {
            'initial_cash': self.initial_cash,
            'cash': self.cash,
            'positions': {symbol: pos.to_dict() for symbol, pos in self.positions.items()},
            'trade_history': self.trade_history
        }
        
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(data, f, indent=2, ensure_ascii=False)
    
    def load_from_file(self, filename: str):
        """Načte portfolio ze souboru"""
        try:
            with open(filename, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            self.initial_cash = data['initial_cash']
            self.cash = data['cash']
            self.positions = {symbol: Position.from_dict(pos_data) 
                            for symbol, pos_data in data['positions'].items()}
            self.trade_history = data['trade_history']
            
            return True
        except FileNotFoundError:
            print(f"Soubor {filename} nenalezen. Začínám s novým portfoliem.")
            return False
        except Exception as e:
            print(f"Chyba při načítání portfolia: {e}")
            return False
