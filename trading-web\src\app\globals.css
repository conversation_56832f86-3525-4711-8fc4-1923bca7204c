@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
    --primary: 221.2 83.2% 53.3%;
    --primary-foreground: 210 40% 98%;
    --secondary: 210 40% 96%;
    --secondary-foreground: 222.2 84% 4.9%;
    --muted: 210 40% 96%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --accent: 210 40% 96%;
    --accent-foreground: 222.2 84% 4.9%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 221.2 83.2% 53.3%;
    --radius: 0.5rem;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    --primary: 217.2 91.2% 59.8%;
    --primary-foreground: 222.2 84% 4.9%;
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 224.3 76.3% 94.1%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  @apply bg-muted;
}

::-webkit-scrollbar-thumb {
  @apply bg-muted-foreground/30 rounded-full;
}

::-webkit-scrollbar-thumb:hover {
  @apply bg-muted-foreground/50;
}

/* Trading specific styles */
.price-up {
  @apply text-green-600 dark:text-green-400;
}

.price-down {
  @apply text-red-600 dark:text-red-400;
}

.price-neutral {
  @apply text-gray-600 dark:text-gray-400;
}

/* Chart container */
.chart-container {
  @apply w-full h-full min-h-[300px];
}

/* Loading animation */
.loading-pulse {
  @apply animate-pulse bg-muted rounded;
}

/* Trading card animations */
.trading-card {
  @apply transition-all duration-200 hover:shadow-lg hover:scale-[1.02];
}

/* Portfolio summary cards */
.portfolio-card {
  @apply bg-gradient-to-br from-background to-muted/50 border-2;
}

.portfolio-card.positive {
  @apply border-green-200 dark:border-green-800;
}

.portfolio-card.negative {
  @apply border-red-200 dark:border-red-800;
}

/* Market status indicator */
.market-status {
  @apply inline-flex items-center gap-2 px-3 py-1 rounded-full text-xs font-medium;
}

.market-status.open {
  @apply bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200;
}

.market-status.closed {
  @apply bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200;
}

/* Trade button styles */
.trade-button {
  @apply relative overflow-hidden transition-all duration-200;
}

.trade-button:hover {
  @apply transform scale-105;
}

.trade-button.buy {
  @apply bg-gradient-to-r from-green-600 to-green-700 hover:from-green-700 hover:to-green-800;
}

.trade-button.sell {
  @apply bg-gradient-to-r from-red-600 to-red-700 hover:from-red-700 hover:to-red-800;
}

/* Responsive grid */
.trading-grid {
  @apply grid gap-6;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
}

@media (min-width: 1024px) {
  .trading-grid {
    grid-template-columns: 1fr 2fr 1fr;
  }
}

/* Mobile optimizations */
@media (max-width: 768px) {
  .trading-grid {
    @apply grid-cols-1 gap-4;
  }
  
  .chart-container {
    @apply min-h-[250px];
  }
}

/* Focus styles for accessibility */
.focus-ring {
  @apply focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2;
}

/* Custom animations */
@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.slide-in-up {
  animation: slideInUp 0.3s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.fade-in {
  animation: fadeIn 0.3s ease-out;
}

/* Price ticker animation */
@keyframes ticker {
  0% {
    transform: translateX(100%);
  }
  100% {
    transform: translateX(-100%);
  }
}

.ticker {
  animation: ticker 30s linear infinite;
}

/* Glassmorphism effect */
.glass {
  @apply backdrop-blur-sm bg-white/10 dark:bg-black/10 border border-white/20 dark:border-white/10;
}

/* Trading table styles */
.trading-table {
  @apply w-full border-collapse;
}

.trading-table th {
  @apply text-left font-medium text-muted-foreground border-b border-border p-3;
}

.trading-table td {
  @apply p-3 border-b border-border/50;
}

.trading-table tr:hover {
  @apply bg-muted/50;
}
