'use client'

import { useState } from 'react'
import { useTradingStore } from '@/lib/store'
import { formatCurrency, formatPercent } from '@/lib/utils'
import { Button } from '@/components/ui/button'
import { Card } from '@/components/ui/card'

export default function Header() {
  const { portfolio, resetPortfolio } = useTradingStore()
  const [showResetConfirm, setShowResetConfirm] = useState(false)

  const handleReset = () => {
    if (showResetConfirm) {
      resetPortfolio()
      setShowResetConfirm(false)
    } else {
      setShowResetConfirm(true)
      // Auto-hide po 3 sekundách
      setTimeout(() => setShowResetConfirm(false), 3000)
    }
  }

  return (
    <header className="border-b bg-card/50 backdrop-blur-sm sticky top-0 z-50">
      <div className="container mx-auto px-4 py-4">
        <div className="flex items-center justify-between">
          {/* Logo a název */}
          <div className="flex items-center space-x-4">
            <div className="flex items-center space-x-2">
              <div className="w-8 h-8 bg-primary rounded-lg flex items-center justify-center">
                <span className="text-primary-foreground font-bold text-lg">T</span>
              </div>
              <div>
                <h1 className="text-xl font-bold">Trading App</h1>
                <p className="text-xs text-muted-foreground">Simulace obchodování</p>
              </div>
            </div>
          </div>

          {/* Portfolio souhrn */}
          {portfolio && (
            <div className="hidden md:flex items-center space-x-6">
              <div className="text-right">
                <p className="text-sm text-muted-foreground">Celková hodnota</p>
                <p className="text-lg font-semibold">
                  {formatCurrency(portfolio.totalValue)}
                </p>
              </div>
              
              <div className="text-right">
                <p className="text-sm text-muted-foreground">P&L</p>
                <p className={`text-lg font-semibold ${
                  portfolio.totalPnL >= 0 ? 'price-up' : 'price-down'
                }`}>
                  {formatCurrency(portfolio.totalPnL)} 
                  <span className="text-sm ml-1">
                    ({formatPercent(portfolio.totalPnLPercent)})
                  </span>
                </p>
              </div>

              <div className="text-right">
                <p className="text-sm text-muted-foreground">Hotovost</p>
                <p className="text-lg font-semibold">
                  {formatCurrency(portfolio.cash)}
                </p>
              </div>
            </div>
          )}

          {/* Akce */}
          <div className="flex items-center space-x-2">
            {/* Market status */}
            <div className="market-status open">
              <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
              <span>Trh otevřen</span>
            </div>

            {/* Reset portfolio */}
            <Button
              variant={showResetConfirm ? "destructive" : "outline"}
              size="sm"
              onClick={handleReset}
              className="ml-4"
            >
              {showResetConfirm ? "Potvrdit reset" : "Reset portfolia"}
            </Button>
          </div>
        </div>

        {/* Mobilní portfolio souhrn */}
        {portfolio && (
          <div className="md:hidden mt-4">
            <Card className="p-4">
              <div className="grid grid-cols-3 gap-4 text-center">
                <div>
                  <p className="text-xs text-muted-foreground">Celková hodnota</p>
                  <p className="font-semibold">
                    {formatCurrency(portfolio.totalValue)}
                  </p>
                </div>
                <div>
                  <p className="text-xs text-muted-foreground">P&L</p>
                  <p className={`font-semibold ${
                    portfolio.totalPnL >= 0 ? 'price-up' : 'price-down'
                  }`}>
                    {formatCurrency(portfolio.totalPnL)}
                  </p>
                </div>
                <div>
                  <p className="text-xs text-muted-foreground">Hotovost</p>
                  <p className="font-semibold">
                    {formatCurrency(portfolio.cash)}
                  </p>
                </div>
              </div>
            </Card>
          </div>
        )}
      </div>
    </header>
  )
}
