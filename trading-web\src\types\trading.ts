// Trading aplikace typy

export interface Asset {
  symbol: string;
  name: string;
  price: number;
  change: number;
  changePercent: number;
  volume?: number;
  marketCap?: number;
  currency: string;
  sector?: string;
  industry?: string;
}

export interface Position {
  id: string;
  symbol: string;
  quantity: number;
  entryPrice: number;
  currentPrice: number;
  entryTime: string;
  marketValue: number;
  unrealizedPnL: number;
  unrealizedPnLPercent: number;
  type: 'long' | 'short';
}

export interface Trade {
  id: string;
  symbol: string;
  type: 'buy' | 'sell';
  quantity: number;
  price: number;
  totalValue: number;
  timestamp: string;
  status: 'pending' | 'completed' | 'cancelled';
}

export interface Portfolio {
  id: string;
  cash: number;
  totalValue: number;
  totalPnL: number;
  totalPnLPercent: number;
  positions: Position[];
  tradeHistory: Trade[];
  initialCash: number;
}

export interface MarketData {
  symbol: string;
  price: number;
  open: number;
  high: number;
  low: number;
  close: number;
  volume: number;
  timestamp: string;
}

export interface ChartData {
  timestamp: string;
  open: number;
  high: number;
  low: number;
  close: number;
  volume: number;
}

export interface OrderRequest {
  symbol: string;
  type: 'buy' | 'sell';
  quantity: number;
  orderType: 'market' | 'limit';
  limitPrice?: number;
}

export interface OrderResponse {
  success: boolean;
  trade?: Trade;
  error?: string;
}

export interface WatchlistItem {
  symbol: string;
  name: string;
  price: number;
  change: number;
  changePercent: number;
}

export interface TradingState {
  // Portfolio
  portfolio: Portfolio | null;
  
  // Current asset
  selectedAsset: Asset | null;
  
  // Market data
  marketData: Record<string, MarketData>;
  
  // Watchlist
  watchlist: WatchlistItem[];
  
  // UI state
  isLoading: boolean;
  error: string | null;
  
  // Actions
  setPortfolio: (portfolio: Portfolio) => void;
  setSelectedAsset: (asset: Asset | null) => void;
  updateMarketData: (symbol: string, data: MarketData) => void;
  addToWatchlist: (item: WatchlistItem) => void;
  removeFromWatchlist: (symbol: string) => void;
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
}

export interface ApiResponse<T> {
  success: boolean;
  data?: T;
  error?: string;
}

// Populární symboly podle kategorií
export const POPULAR_SYMBOLS = {
  stocks: [
    { symbol: 'AAPL', name: 'Apple Inc.' },
    { symbol: 'GOOGL', name: 'Alphabet Inc.' },
    { symbol: 'MSFT', name: 'Microsoft Corporation' },
    { symbol: 'TSLA', name: 'Tesla, Inc.' },
    { symbol: 'AMZN', name: 'Amazon.com Inc.' },
    { symbol: 'NVDA', name: 'NVIDIA Corporation' },
    { symbol: 'META', name: 'Meta Platforms Inc.' },
    { symbol: 'NFLX', name: 'Netflix Inc.' }
  ],
  crypto: [
    { symbol: 'BTC-USD', name: 'Bitcoin' },
    { symbol: 'ETH-USD', name: 'Ethereum' },
    { symbol: 'ADA-USD', name: 'Cardano' },
    { symbol: 'DOT-USD', name: 'Polkadot' },
    { symbol: 'SOL-USD', name: 'Solana' },
    { symbol: 'MATIC-USD', name: 'Polygon' }
  ],
  forex: [
    { symbol: 'EURUSD=X', name: 'EUR/USD' },
    { symbol: 'GBPUSD=X', name: 'GBP/USD' },
    { symbol: 'USDJPY=X', name: 'USD/JPY' },
    { symbol: 'USDCHF=X', name: 'USD/CHF' }
  ],
  commodities: [
    { symbol: 'GC=F', name: 'Gold' },
    { symbol: 'SI=F', name: 'Silver' },
    { symbol: 'CL=F', name: 'Crude Oil' },
    { symbol: 'NG=F', name: 'Natural Gas' }
  ]
} as const;

export type AssetCategory = keyof typeof POPULAR_SYMBOLS;
