'use client'

import { useState } from 'react'
import { Asset, Trade } from '@/types/trading'
import { useTradingStore } from '@/lib/store'
import { formatCurrency, validateTradeOrder, calculateTotalCost } from '@/lib/utils'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'

interface TradingPanelProps {
  asset: Asset
}

export default function TradingPanel({ asset }: TradingPanelProps) {
  const { portfolio, executeTrade } = useTradingStore()
  const [quantity, setQuantity] = useState('')
  const [orderType, setOrderType] = useState<'buy' | 'sell'>('buy')
  const [isProcessing, setIsProcessing] = useState(false)
  const [confirmOrder, setConfirmOrder] = useState<{
    type: 'buy' | 'sell'
    quantity: number
    totalValue: number
  } | null>(null)

  // Najít existují<PERSON><PERSON> pozici
  const existingPosition = portfolio?.positions.find(p => p.symbol === asset.symbol)
  const availableShares = existingPosition?.quantity || 0

  // Validace a kalkulace
  const quantityNum = parseFloat(quantity) || 0
  const totalValue = calculateTotalCost(asset.price, quantityNum)
  const validation = validateTradeOrder(
    orderType,
    quantityNum,
    asset.price,
    portfolio?.cash || 0,
    availableShares
  )

  const handleQuantityChange = (value: string) => {
    // Povolit pouze čísla a desetinnou čárku/tečku
    if (value === '' || /^\d*[.,]?\d*$/.test(value)) {
      setQuantity(value.replace(',', '.'))
    }
  }

  const handleQuickAmount = (percentage: number) => {
    if (orderType === 'buy' && portfolio) {
      const maxBuyValue = portfolio.cash * (percentage / 100)
      const maxQuantity = Math.floor((maxBuyValue / asset.price) * 100) / 100
      setQuantity(maxQuantity.toString())
    } else if (orderType === 'sell' && existingPosition) {
      const sellQuantity = Math.floor((existingPosition.quantity * percentage / 100) * 100) / 100
      setQuantity(sellQuantity.toString())
    }
  }

  const handleOrderSubmit = () => {
    if (!validation.isValid || quantityNum <= 0) return

    setConfirmOrder({
      type: orderType,
      quantity: quantityNum,
      totalValue,
    })
  }

  const handleConfirmTrade = async () => {
    if (!confirmOrder || !portfolio) return

    setIsProcessing(true)
    try {
      const trade: Omit<Trade, 'id' | 'timestamp' | 'status'> = {
        symbol: asset.symbol,
        type: confirmOrder.type,
        quantity: confirmOrder.quantity,
        price: asset.price,
        totalValue: confirmOrder.totalValue,
      }

      // Použití type assertion pro executeTrade
      ;(executeTrade as any)(trade)
      
      // Reset formuláře
      setQuantity('')
      setConfirmOrder(null)
    } catch (error) {
      console.error('Chyba při provádění obchodu:', error)
    } finally {
      setIsProcessing(false)
    }
  }

  return (
    <Card className="trading-card">
      <CardHeader>
        <CardTitle className="text-lg">Obchodní panel</CardTitle>
      </CardHeader>

      <CardContent className="space-y-4">
        {/* Order type selector */}
        <div className="flex rounded-md border">
          <Button
            variant={orderType === 'buy' ? 'buy' : 'outline'}
            onClick={() => setOrderType('buy')}
            className="flex-1 rounded-r-none"
          >
            Koupit
          </Button>
          <Button
            variant={orderType === 'sell' ? 'sell' : 'outline'}
            onClick={() => setOrderType('sell')}
            className="flex-1 rounded-l-none"
            disabled={!existingPosition || availableShares <= 0}
          >
            Prodat
          </Button>
        </div>

        {/* Current position info */}
        {existingPosition && (
          <div className="bg-muted/50 rounded-lg p-3 space-y-2">
            <h4 className="font-medium text-sm">Aktuální pozice</h4>
            <div className="grid grid-cols-2 gap-2 text-sm">
              <div>
                <span className="text-muted-foreground">Množství: </span>
                <span className="font-medium">{existingPosition.quantity}</span>
              </div>
              <div>
                <span className="text-muted-foreground">Vstupní cena: </span>
                <span className="font-medium">{formatCurrency(existingPosition.entryPrice)}</span>
              </div>
              <div>
                <span className="text-muted-foreground">Tržní hodnota: </span>
                <span className="font-medium">{formatCurrency(existingPosition.marketValue)}</span>
              </div>
              <div>
                <span className="text-muted-foreground">P&L: </span>
                <span className={`font-medium ${
                  existingPosition.unrealizedPnL >= 0 ? 'price-up' : 'price-down'
                }`}>
                  {formatCurrency(existingPosition.unrealizedPnL)}
                </span>
              </div>
            </div>
          </div>
        )}

        {/* Quantity input */}
        <div className="space-y-2">
          <label className="text-sm font-medium">
            Množství {orderType === 'sell' && `(max: ${availableShares})`}
          </label>
          <Input
            type="text"
            value={quantity}
            onChange={(e) => handleQuantityChange(e.target.value)}
            placeholder="0.00"
            className="text-right"
          />
        </div>

        {/* Quick amount buttons */}
        <div className="grid grid-cols-4 gap-2">
          {[25, 50, 75, 100].map((percentage) => (
            <Button
              key={percentage}
              variant="outline"
              size="sm"
              onClick={() => handleQuickAmount(percentage)}
              disabled={
                (orderType === 'buy' && !portfolio?.cash) ||
                (orderType === 'sell' && !existingPosition)
              }
            >
              {percentage}%
            </Button>
          ))}
        </div>

        {/* Order summary */}
        {quantityNum > 0 && (
          <div className="bg-muted/50 rounded-lg p-3 space-y-2">
            <h4 className="font-medium text-sm">Souhrn objednávky</h4>
            <div className="space-y-1 text-sm">
              <div className="flex justify-between">
                <span className="text-muted-foreground">Cena za jednotku:</span>
                <span className="font-medium">{formatCurrency(asset.price)}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-muted-foreground">Množství:</span>
                <span className="font-medium">{quantityNum}</span>
              </div>
              <div className="flex justify-between border-t pt-2">
                <span className="text-muted-foreground">Celková hodnota:</span>
                <span className="font-medium">{formatCurrency(totalValue)}</span>
              </div>
            </div>
          </div>
        )}

        {/* Validation error */}
        {!validation.isValid && quantityNum > 0 && (
          <div className="text-destructive text-sm bg-destructive/10 rounded-lg p-3">
            {validation.error}
          </div>
        )}

        {/* Submit button */}
        <Button
          onClick={handleOrderSubmit}
          disabled={!validation.isValid || quantityNum <= 0 || isProcessing}
          variant={orderType === 'buy' ? 'buy' : 'sell'}
          className="w-full"
        >
          {isProcessing ? 'Zpracování...' : 
           orderType === 'buy' ? `Koupit za ${formatCurrency(totalValue)}` :
           `Prodat za ${formatCurrency(totalValue)}`}
        </Button>

        {/* Available cash/shares info */}
        <div className="text-xs text-muted-foreground text-center">
          {orderType === 'buy' ? 
            `Dostupná hotovost: ${formatCurrency(portfolio?.cash || 0)}` :
            `Dostupné akcie: ${availableShares}`
          }
        </div>
      </CardContent>

      {/* Confirmation dialog */}
      {confirmOrder && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
          <Card className="w-full max-w-md">
            <CardHeader>
              <CardTitle>Potvrdit objednávku</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <p className="text-sm">
                  <strong>Typ:</strong> {confirmOrder.type === 'buy' ? 'Nákup' : 'Prodej'}
                </p>
                <p className="text-sm">
                  <strong>Symbol:</strong> {asset.symbol}
                </p>
                <p className="text-sm">
                  <strong>Množství:</strong> {confirmOrder.quantity}
                </p>
                <p className="text-sm">
                  <strong>Cena:</strong> {formatCurrency(asset.price)}
                </p>
                <p className="text-sm">
                  <strong>Celková hodnota:</strong> {formatCurrency(confirmOrder.totalValue)}
                </p>
              </div>

              <div className="flex space-x-2">
                <Button
                  variant="outline"
                  onClick={() => setConfirmOrder(null)}
                  className="flex-1"
                  disabled={isProcessing}
                >
                  Zrušit
                </Button>
                <Button
                  onClick={handleConfirmTrade}
                  variant={confirmOrder.type === 'buy' ? 'buy' : 'sell'}
                  className="flex-1"
                  disabled={isProcessing}
                >
                  {isProcessing ? 'Zpracování...' : 'Potvrdit'}
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      )}
    </Card>
  )
}
