'use client'

import { useState, useRef, useEffect } from 'react'
import { useTradingStore } from '@/lib/store'
import { tradingAPI } from '@/lib/api'
import { POPULAR_SYMBOLS, AssetCategory } from '@/types/trading'
import { isValidSymbol, debounce } from '@/lib/utils'
import { Input } from '@/components/ui/input'
import { Button } from '@/components/ui/button'
import { Card, CardContent } from '@/components/ui/card'

export default function AssetSearch() {
  const { setSelectedAsset, setLoading, setError, addToWatchlist } = useTradingStore()
  const [searchTerm, setSearchTerm] = useState('')
  const [isSearching, setIsSearching] = useState(false)
  const [showSuggestions, setShowSuggestions] = useState(false)
  const [selectedCategory, setSelectedCategory] = useState<AssetCategory>('stocks')
  const searchRef = useRef<HTMLDivElement>(null)

  // Debounced search function
  const debouncedSearch = useRef(
    debounce(async (term: string) => {
      if (!term.trim() || !isValidSymbol(term)) return

      setIsSearching(true)
      try {
        const response = await tradingAPI.getAssetInfo(term.toUpperCase())
        if (response.success && response.data) {
          setSelectedAsset(response.data)
          setSearchTerm('')
          setShowSuggestions(false)
          setError(null)
        } else {
          setError(`Symbol "${term}" nebyl nalezen`)
        }
      } catch (error) {
        setError('Chyba při vyhledávání symbolu')
      } finally {
        setIsSearching(false)
      }
    }, 500)
  ).current

  // Handle search input change
  const handleSearchChange = (value: string) => {
    setSearchTerm(value)
    setShowSuggestions(value.length > 0)
    
    if (value.length >= 2) {
      debouncedSearch(value)
    }
  }

  // Handle symbol selection
  const handleSymbolSelect = async (symbol: string) => {
    setLoading(true)
    setShowSuggestions(false)
    setSearchTerm('')
    
    try {
      const response = await tradingAPI.getAssetInfo(symbol)
      if (response.success && response.data) {
        setSelectedAsset(response.data)
        setError(null)
      } else {
        setError(`Nepodařilo se načíst informace o ${symbol}`)
      }
    } catch (error) {
      setError('Chyba při načítání aktiva')
    } finally {
      setLoading(false)
    }
  }

  // Handle add to watchlist
  const handleAddToWatchlist = async (symbol: string, name: string) => {
    try {
      const response = await tradingAPI.getCurrentPrice(symbol)
      if (response.success && response.data) {
        addToWatchlist({
          symbol,
          name,
          price: response.data,
          change: 0,
          changePercent: 0,
        })
      }
    } catch (error) {
      console.error('Chyba při přidávání do watchlistu:', error)
    }
  }

  // Close suggestions when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (searchRef.current && !searchRef.current.contains(event.target as Node)) {
        setShowSuggestions(false)
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => document.removeEventListener('mousedown', handleClickOutside)
  }, [])

  return (
    <div className="space-y-4">
      {/* Search input */}
      <div ref={searchRef} className="relative">
        <div className="flex space-x-2">
          <div className="flex-1 relative">
            <Input
              type="text"
              placeholder="Vyhledat symbol (např. AAPL, BTC-USD)..."
              value={searchTerm}
              onChange={(e) => handleSearchChange(e.target.value)}
              onFocus={() => setShowSuggestions(searchTerm.length > 0)}
              className="pr-10"
            />
            {isSearching && (
              <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
                <div className="w-4 h-4 border-2 border-primary border-t-transparent rounded-full animate-spin"></div>
              </div>
            )}
          </div>
          <Button
            onClick={() => handleSearchChange(searchTerm)}
            disabled={!searchTerm.trim() || isSearching}
          >
            Vyhledat
          </Button>
        </div>

        {/* Search suggestions */}
        {showSuggestions && searchTerm.length > 0 && (
          <Card className="absolute top-full left-0 right-0 mt-1 z-50 max-h-60 overflow-y-auto">
            <CardContent className="p-2">
              <div className="text-sm text-muted-foreground mb-2">
                Stiskněte Enter pro vyhledání "{searchTerm.toUpperCase()}"
              </div>
            </CardContent>
          </Card>
        )}
      </div>

      {/* Popular symbols by category */}
      <Card>
        <CardContent className="p-4">
          <div className="space-y-4">
            {/* Category tabs */}
            <div className="flex flex-wrap gap-2">
              {Object.keys(POPULAR_SYMBOLS).map((category) => (
                <Button
                  key={category}
                  variant={selectedCategory === category ? "default" : "outline"}
                  size="sm"
                  onClick={() => setSelectedCategory(category as AssetCategory)}
                  className="capitalize"
                >
                  {category === 'stocks' && 'Akcie'}
                  {category === 'crypto' && 'Krypto'}
                  {category === 'forex' && 'Forex'}
                  {category === 'commodities' && 'Komodity'}
                </Button>
              ))}
            </div>

            {/* Popular symbols grid */}
            <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-2">
              {POPULAR_SYMBOLS[selectedCategory].map((item) => (
                <div
                  key={item.symbol}
                  className="group relative"
                >
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleSymbolSelect(item.symbol)}
                    className="w-full text-left justify-start hover:bg-primary hover:text-primary-foreground"
                  >
                    <div className="truncate">
                      <div className="font-medium text-xs">{item.symbol}</div>
                      <div className="text-xs opacity-70 truncate">{item.name}</div>
                    </div>
                  </Button>
                  
                  {/* Add to watchlist button */}
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={(e) => {
                      e.stopPropagation()
                      handleAddToWatchlist(item.symbol, item.name)
                    }}
                    className="absolute top-0 right-0 opacity-0 group-hover:opacity-100 transition-opacity p-1 h-6 w-6"
                    title="Přidat do watchlistu"
                  >
                    +
                  </Button>
                </div>
              ))}
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
