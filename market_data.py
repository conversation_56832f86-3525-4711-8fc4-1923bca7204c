"""
Modul pro získávání tržních dat z Yahoo Finance API
"""

import yfinance as yf
import pandas as pd
from datetime import datetime, timedelta
from typing import Optional, Dict, Any


class MarketDataProvider:
    """Třída pro získávání tržních dat"""
    
    def __init__(self):
        self.cache = {}  # Jednoduchá cache pro data
    
    def get_current_price(self, symbol: str) -> Optional[float]:
        """
        Získá aktuální cenu aktiva
        
        Args:
            symbol: Symbol aktiva (např. 'AAPL', 'BTC-USD')
            
        Returns:
            Aktuální cena nebo None při chybě
        """
        try:
            ticker = yf.Ticker(symbol)
            info = ticker.info
            
            # Zkusíme různé klíče pro aktuální cenu
            price_keys = ['currentPrice', 'regularMarketPrice', 'price', 'ask', 'bid']
            
            for key in price_keys:
                if key in info and info[key] is not None:
                    return float(info[key])
            
            # Pokud info neobsahuje cenu, zkusíme poslední zavírací cenu
            hist = ticker.history(period="1d")
            if not hist.empty:
                return float(hist['Close'].iloc[-1])
                
            return None
            
        except Exception as e:
            print(f"Chyba při získávání ceny pro {symbol}: {e}")
            return None
    
    def get_historical_data(self, symbol: str, period: str = "1mo") -> Optional[pd.DataFrame]:
        """
        Získá historická data aktiva
        
        Args:
            symbol: Symbol aktiva
            period: Období (1d, 5d, 1mo, 3mo, 6mo, 1y, 2y, 5y, 10y, ytd, max)
            
        Returns:
            DataFrame s historickými daty nebo None při chybě
        """
        try:
            ticker = yf.Ticker(symbol)
            hist = ticker.history(period=period)
            
            if hist.empty:
                print(f"Žádná data pro symbol {symbol}")
                return None
                
            return hist
            
        except Exception as e:
            print(f"Chyba při získávání historických dat pro {symbol}: {e}")
            return None
    
    def get_asset_info(self, symbol: str) -> Dict[str, Any]:
        """
        Získá základní informace o aktivu
        
        Args:
            symbol: Symbol aktiva
            
        Returns:
            Slovník s informacemi o aktivu
        """
        try:
            ticker = yf.Ticker(symbol)
            info = ticker.info
            
            return {
                'symbol': symbol,
                'name': info.get('longName', info.get('shortName', symbol)),
                'currency': info.get('currency', 'USD'),
                'market_cap': info.get('marketCap'),
                'sector': info.get('sector'),
                'industry': info.get('industry')
            }
            
        except Exception as e:
            print(f"Chyba při získávání informací pro {symbol}: {e}")
            return {'symbol': symbol, 'name': symbol}
    
    def validate_symbol(self, symbol: str) -> bool:
        """
        Ověří, zda je symbol validní
        
        Args:
            symbol: Symbol aktiva
            
        Returns:
            True pokud je symbol validní
        """
        try:
            ticker = yf.Ticker(symbol)
            info = ticker.info
            
            # Pokud info obsahuje základní informace, symbol je pravděpodobně validní
            return 'symbol' in info or 'shortName' in info or 'longName' in info
            
        except:
            return False


# Přednastavené symboly pro snadné testování
POPULAR_SYMBOLS = {
    'stocks': ['AAPL', 'GOOGL', 'MSFT', 'TSLA', 'AMZN', 'NVDA'],
    'crypto': ['BTC-USD', 'ETH-USD', 'ADA-USD', 'DOT-USD'],
    'forex': ['EURUSD=X', 'GBPUSD=X', 'USDJPY=X'],
    'commodities': ['GC=F', 'SI=F', 'CL=F']  # Gold, Silver, Oil
}


def get_popular_symbols() -> Dict[str, list]:
    """Vrátí slovník s populárními symboly podle kategorií"""
    return POPULAR_SYMBOLS
