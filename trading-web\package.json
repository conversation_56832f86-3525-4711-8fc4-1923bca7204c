{"name": "trading-web", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"next": "14.2.5", "react": "^18.3.1", "react-dom": "^18.3.1", "typescript": "^5.5.4", "tailwindcss": "^3.4.7", "autoprefixer": "^10.4.19", "postcss": "^8.4.40", "lucide-react": "^0.427.0", "recharts": "^2.12.7", "zustand": "^4.5.4", "clsx": "^2.1.1", "class-variance-authority": "^0.7.0", "tailwind-merge": "^2.4.0", "@radix-ui/react-slot": "^1.1.0", "tailwindcss-animate": "^1.0.7"}, "devDependencies": {"@types/node": "^20.14.12", "@types/react": "^18.3.3", "@types/react-dom": "^18.3.0", "eslint": "^8.57.0", "eslint-config-next": "14.2.5", "postcss": "^8.4.40", "tailwindcss": "^3.4.7", "typescript": "^5.5.4"}}