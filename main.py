"""
Hlavní soubor pro jednoduchou trading aplikaci
"""

import sys
from typing import Dict, List
from market_data import get_popular_symbols
from trading_engine import TradingEngine


class TradingApp:
    """Hlavní třída aplikace"""
    
    def __init__(self):
        self.engine = TradingEngine()
        self.running = True
    
    def show_welcome_message(self):
        """Zobrazí uvítací zprávu"""
        print("="*60)
        print("    VÍTEJTE V JEDNODUCHÉ TRADING APLIKACI")
        print("="*60)
        print("Tato aplikace umožňuje:")
        print("• Sledování aktuálních cen akcií a kryptoměn")
        print("• Vykreslování cenových grafů")
        print("• Simulaci nákupu a prodeje aktiv")
        print("• Správu portfolia")
        print("="*60)
    
    def show_main_menu(self):
        """Zobrazí hlavní menu"""
        print(f"\n{'='*40}")
        print("           HLAVNÍ MENU")
        print(f"{'='*40}")
        print("1. Nastavit aktivní symbol")
        print("2. Zobrazit aktuální cenu")
        print("3. Zobrazit cenový graf")
        print("4. Koupit aktivum")
        print("5. Prodat aktivum")
        print("6. Zobrazit portfolio")
        print("7. Uložit portfolio")
        print("8. Načíst portfolio")
        print("9. Zobrazit populární symboly")
        print("0. Ukončit aplikaci")
        print(f"{'='*40}")
        
        if self.engine.current_symbol:
            print(f"Aktivní symbol: {self.engine.current_symbol}")
            if self.engine.current_price:
                print(f"Poslední cena: ${self.engine.current_price:.2f}")
        else:
            print("Žádný aktivní symbol není nastaven")
        
        print(f"Dostupná hotovost: ${self.engine.portfolio.get_available_cash():.2f}")
    
    def show_popular_symbols(self):
        """Zobrazí populární symboly podle kategorií"""
        symbols = get_popular_symbols()
        
        print(f"\n{'='*50}")
        print("         POPULÁRNÍ SYMBOLY")
        print(f"{'='*50}")
        
        for category, symbol_list in symbols.items():
            print(f"\n{category.upper()}:")
            for i, symbol in enumerate(symbol_list, 1):
                print(f"  {i}. {symbol}")
        
        print(f"\nPro kryptoměny použijte formát: BTC-USD, ETH-USD")
        print(f"Pro forex použijte formát: EURUSD=X")
        print(f"Pro komodity použijte formát: GC=F (zlato), CL=F (ropa)")
    
    def get_user_input(self, prompt: str, input_type: type = str):
        """
        Získá vstup od uživatele s validací typu
        
        Args:
            prompt: Text výzvy
            input_type: Očekávaný typ vstupu
            
        Returns:
            Validovaný vstup nebo None při chybě
        """
        try:
            user_input = input(prompt).strip()
            if input_type == float:
                return float(user_input)
            elif input_type == int:
                return int(user_input)
            else:
                return user_input
        except ValueError:
            print(f"Neplatný vstup! Očekáván typ: {input_type.__name__}")
            return None
        except KeyboardInterrupt:
            print("\nOperace zrušena uživatelem.")
            return None
    
    def handle_set_symbol(self):
        """Zpracuje nastavení aktivního symbolu"""
        print(f"\n{'='*40}")
        print("      NASTAVENÍ AKTIVNÍHO SYMBOLU")
        print(f"{'='*40}")
        
        symbol = self.get_user_input("Zadejte symbol aktiva (např. AAPL, BTC-USD): ")
        if symbol:
            self.engine.set_active_symbol(symbol)
    
    def handle_show_price(self):
        """Zpracuje zobrazení aktuální ceny"""
        if not self.engine.current_symbol:
            print("Nejprve nastavte aktivní symbol!")
            return
        
        print(f"\nZískávám aktuální cenu pro {self.engine.current_symbol}...")
        price = self.engine.get_current_price()
        
        if price:
            print(f"Aktuální cena {self.engine.current_symbol}: ${price:.2f}")
        else:
            print("Nepodařilo se získat aktuální cenu.")
    
    def handle_show_chart(self):
        """Zpracuje zobrazení cenového grafu"""
        if not self.engine.current_symbol:
            print("Nejprve nastavte aktivní symbol!")
            return
        
        print(f"\nDostupná období:")
        periods = {
            '1': '1d',
            '2': '5d', 
            '3': '1mo',
            '4': '3mo',
            '5': '6mo',
            '6': '1y'
        }
        
        for key, period in periods.items():
            print(f"{key}. {period}")
        
        choice = self.get_user_input("Vyberte období (1-6) [default: 3]: ")
        if not choice:
            choice = '3'
        
        period = periods.get(choice, '1mo')
        self.engine.plot_price_chart(period=period)
    
    def handle_buy_order(self):
        """Zpracuje nákupní příkaz"""
        if not self.engine.current_symbol:
            print("Nejprve nastavte aktivní symbol!")
            return
        
        print(f"\n{'='*40}")
        print(f"    NÁKUP - {self.engine.current_symbol}")
        print(f"{'='*40}")
        
        # Zobrazíme aktuální cenu
        current_price = self.engine.get_current_price()
        if not current_price:
            print("Nepodařilo se získat aktuální cenu.")
            return
        
        print(f"Aktuální cena: ${current_price:.2f}")
        print(f"Dostupná hotovost: ${self.engine.portfolio.get_available_cash():.2f}")
        
        quantity = self.get_user_input("Zadejte množství k nákupu: ", float)
        if quantity and quantity > 0:
            self.engine.execute_buy_order(quantity)
        else:
            print("Neplatné množství!")
    
    def handle_sell_order(self):
        """Zpracuje prodejní příkaz"""
        if not self.engine.current_symbol:
            print("Nejprve nastavte aktivní symbol!")
            return
        
        position = self.engine.portfolio.get_position(self.engine.current_symbol)
        if not position or position.quantity <= 0:
            print(f"Nemáte žádné akcie {self.engine.current_symbol} k prodeji!")
            return
        
        print(f"\n{'='*40}")
        print(f"    PRODEJ - {self.engine.current_symbol}")
        print(f"{'='*40}")
        
        # Zobrazíme aktuální cenu a pozici
        current_price = self.engine.get_current_price()
        if not current_price:
            print("Nepodařilo se získat aktuální cenu.")
            return
        
        print(f"Aktuální cena: ${current_price:.2f}")
        print(f"Dostupné množství: {position.quantity}")
        print(f"Vstupní cena: ${position.entry_price:.2f}")
        
        unrealized_pnl = position.get_unrealized_pnl(current_price)
        unrealized_pnl_percent = position.get_unrealized_pnl_percent(current_price)
        print(f"Nerealizovaný P&L: ${unrealized_pnl:.2f} ({unrealized_pnl_percent:.2f}%)")
        
        quantity = self.get_user_input("Zadejte množství k prodeji: ", float)
        if quantity and 0 < quantity <= position.quantity:
            self.engine.execute_sell_order(quantity)
        else:
            print("Neplatné množství!")
    
    def handle_show_portfolio(self):
        """Zpracuje zobrazení portfolia"""
        self.engine.show_portfolio_summary()
    
    def handle_save_portfolio(self):
        """Zpracuje uložení portfolia"""
        filename = self.get_user_input("Zadejte název souboru [portfolio.json]: ")
        if not filename:
            filename = "portfolio.json"
        
        self.engine.save_portfolio(filename)
    
    def handle_load_portfolio(self):
        """Zpracuje načtení portfolia"""
        filename = self.get_user_input("Zadejte název souboru [portfolio.json]: ")
        if not filename:
            filename = "portfolio.json"
        
        self.engine.load_portfolio(filename)
    
    def run(self):
        """Hlavní smyčka aplikace"""
        self.show_welcome_message()
        
        # Pokusíme se načíst existující portfolio
        self.engine.load_portfolio()
        
        while self.running:
            try:
                self.show_main_menu()
                
                choice = self.get_user_input("\nVyberte možnost (0-9): ")
                
                if choice == '1':
                    self.handle_set_symbol()
                elif choice == '2':
                    self.handle_show_price()
                elif choice == '3':
                    self.handle_show_chart()
                elif choice == '4':
                    self.handle_buy_order()
                elif choice == '5':
                    self.handle_sell_order()
                elif choice == '6':
                    self.handle_show_portfolio()
                elif choice == '7':
                    self.handle_save_portfolio()
                elif choice == '8':
                    self.handle_load_portfolio()
                elif choice == '9':
                    self.show_popular_symbols()
                elif choice == '0':
                    print("\nDěkujeme za použití aplikace!")
                    # Automaticky uložíme portfolio před ukončením
                    self.engine.save_portfolio()
                    self.running = False
                else:
                    print("Neplatná volba! Zadejte číslo 0-9.")
                
                if self.running and choice != '0':
                    input("\nStiskněte Enter pro pokračování...")
                    
            except KeyboardInterrupt:
                print("\n\nUkončování aplikace...")
                self.engine.save_portfolio()
                self.running = False
            except Exception as e:
                print(f"\nNastala chyba: {e}")
                print("Aplikace pokračuje...")


def main():
    """Hlavní funkce"""
    app = TradingApp()
    app.run()


if __name__ == "__main__":
    main()
