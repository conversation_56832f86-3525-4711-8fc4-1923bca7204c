'use client'

import { useState, useEffect } from 'react'
import { Asset } from '@/types/trading'
import { tradingAPI } from '@/lib/api'
import { formatCurrency, formatPercent, formatCompactNumber, getPriceChangeColor, getPriceChangeIcon } from '@/lib/utils'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'

interface AssetInfoProps {
  asset: Asset
}

export default function AssetInfo({ asset }: AssetInfoProps) {
  const [currentAsset, setCurrentAsset] = useState(asset)
  const [isUpdating, setIsUpdating] = useState(false)
  const [lastUpdate, setLastUpdate] = useState(new Date())

  // Aktualizace ceny každých 10 sekund
  useEffect(() => {
    const updatePrice = async () => {
      setIsUpdating(true)
      try {
        const response = await tradingAPI.getAssetInfo(asset.symbol)
        if (response.success && response.data) {
          setCurrentAsset(response.data)
          setLastUpdate(new Date())
        }
      } catch (error) {
        console.error('Chyba při aktualizaci ceny:', error)
      } finally {
        setIsUpdating(false)
      }
    }

    // Okamžitá aktualizace při změně symbolu
    if (asset.symbol !== currentAsset.symbol) {
      updatePrice()
    }

    // Pravidelná aktualizace každých 10 sekund
    const interval = setInterval(updatePrice, 10000)

    return () => clearInterval(interval)
  }, [asset.symbol, currentAsset.symbol])

  const handleRefresh = async () => {
    setIsUpdating(true)
    try {
      const response = await tradingAPI.getAssetInfo(asset.symbol)
      if (response.success && response.data) {
        setCurrentAsset(response.data)
        setLastUpdate(new Date())
      }
    } catch (error) {
      console.error('Chyba při aktualizaci:', error)
    } finally {
      setIsUpdating(false)
    }
  }

  return (
    <Card className="trading-card">
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="text-2xl font-bold">{currentAsset.symbol}</CardTitle>
            <p className="text-muted-foreground">{currentAsset.name}</p>
          </div>
          <Button
            variant="outline"
            size="sm"
            onClick={handleRefresh}
            disabled={isUpdating}
            className="ml-4"
          >
            {isUpdating ? (
              <div className="w-4 h-4 border-2 border-current border-t-transparent rounded-full animate-spin" />
            ) : (
              '↻'
            )}
          </Button>
        </div>
      </CardHeader>

      <CardContent className="space-y-4">
        {/* Hlavní cena */}
        <div className="space-y-2">
          <div className="flex items-baseline space-x-3">
            <span className="text-3xl font-bold">
              {formatCurrency(currentAsset.price, currentAsset.currency)}
            </span>
            <div className={`flex items-center space-x-1 ${getPriceChangeColor(currentAsset.change)}`}>
              <span className="text-lg">{getPriceChangeIcon(currentAsset.change)}</span>
              <span className="font-medium">
                {formatCurrency(currentAsset.change, currentAsset.currency)}
              </span>
              <span className="text-sm">
                ({formatPercent(currentAsset.changePercent)})
              </span>
            </div>
          </div>
          
          <p className="text-xs text-muted-foreground">
            Poslední aktualizace: {lastUpdate.toLocaleTimeString('cs-CZ')}
          </p>
        </div>

        {/* Statistiky */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 pt-4 border-t">
          {currentAsset.volume && (
            <div className="text-center">
              <p className="text-xs text-muted-foreground">Objem</p>
              <p className="font-semibold">{formatCompactNumber(currentAsset.volume)}</p>
            </div>
          )}
          
          {currentAsset.marketCap && (
            <div className="text-center">
              <p className="text-xs text-muted-foreground">Tržní kap.</p>
              <p className="font-semibold">{formatCompactNumber(currentAsset.marketCap)}</p>
            </div>
          )}
          
          {currentAsset.sector && (
            <div className="text-center">
              <p className="text-xs text-muted-foreground">Sektor</p>
              <p className="font-semibold text-xs">{currentAsset.sector}</p>
            </div>
          )}
          
          {currentAsset.industry && (
            <div className="text-center">
              <p className="text-xs text-muted-foreground">Odvětví</p>
              <p className="font-semibold text-xs">{currentAsset.industry}</p>
            </div>
          )}
        </div>

        {/* Indikátory */}
        <div className="flex items-center justify-between pt-4 border-t">
          <div className="flex items-center space-x-4">
            {/* Trend indikátor */}
            <div className="flex items-center space-x-2">
              <div className={`w-3 h-3 rounded-full ${
                currentAsset.changePercent > 2 ? 'bg-green-500' :
                currentAsset.changePercent > 0 ? 'bg-green-300' :
                currentAsset.changePercent < -2 ? 'bg-red-500' :
                currentAsset.changePercent < 0 ? 'bg-red-300' :
                'bg-gray-400'
              }`}></div>
              <span className="text-xs text-muted-foreground">
                {currentAsset.changePercent > 2 ? 'Silně rostoucí' :
                 currentAsset.changePercent > 0 ? 'Rostoucí' :
                 currentAsset.changePercent < -2 ? 'Silně klesající' :
                 currentAsset.changePercent < 0 ? 'Klesající' :
                 'Stabilní'}
              </span>
            </div>
          </div>

          {/* Volatilita indikátor */}
          <div className="text-right">
            <p className="text-xs text-muted-foreground">Volatilita</p>
            <div className="flex items-center space-x-1">
              {[1, 2, 3, 4, 5].map((level) => (
                <div
                  key={level}
                  className={`w-1 h-3 rounded-sm ${
                    Math.abs(currentAsset.changePercent) >= level * 0.5
                      ? 'bg-primary'
                      : 'bg-muted'
                  }`}
                />
              ))}
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
