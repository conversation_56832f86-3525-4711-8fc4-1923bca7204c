"""
Modul pro simulaci obchodování a správu obchodních příkazů
"""

import matplotlib.pyplot as plt
import matplotlib.dates as mdates
from datetime import datetime
from typing import Optional, Dict, Any
import pandas as pd

from market_data import MarketDataProvider
from portfolio import Portfolio


class TradingEngine:
    """Hlavní třída pro obchodování"""
    
    def __init__(self, initial_cash: float = 10000.0):
        self.market_data = MarketDataProvider()
        self.portfolio = Portfolio(initial_cash)
        self.current_symbol = None
        self.current_price = None
    
    def set_active_symbol(self, symbol: str) -> bool:
        """
        Nastaví aktivní symbol pro obchodování
        
        Args:
            symbol: Symbol aktiva
            
        Returns:
            True pokud byl symbol úspěšně nastaven
        """
        if not self.market_data.validate_symbol(symbol):
            print(f"Symbol '{symbol}' není validní nebo není dos<PERSON>.")
            return False
        
        self.current_symbol = symbol.upper()
        self.current_price = self.market_data.get_current_price(self.current_symbol)
        
        if self.current_price is None:
            print(f"Nepodařilo se získat cenu pro symbol '{self.current_symbol}'.")
            return False
        
        print(f"Aktivní symbol nastaven na: {self.current_symbol}")
        print(f"Aktuální cena: ${self.current_price:.2f}")
        
        return True
    
    def get_current_price(self, symbol: str = None) -> Optional[float]:
        """
        Získá aktuální cenu aktiva
        
        Args:
            symbol: Symbol aktiva (pokud není zadán, použije se aktivní symbol)
            
        Returns:
            Aktuální cena nebo None
        """
        target_symbol = symbol or self.current_symbol
        if not target_symbol:
            print("Není nastaven žádný aktivní symbol.")
            return None
        
        price = self.market_data.get_current_price(target_symbol)
        if symbol is None:  # Aktualizujeme cache pouze pro aktivní symbol
            self.current_price = price
        
        return price
    
    def execute_buy_order(self, quantity: float, symbol: str = None) -> bool:
        """
        Provede nákupní příkaz
        
        Args:
            quantity: Množství k nákupu
            symbol: Symbol aktiva (pokud není zadán, použije se aktivní symbol)
            
        Returns:
            True pokud byl příkaz úspěšně proveden
        """
        target_symbol = symbol or self.current_symbol
        if not target_symbol:
            print("Není nastaven žádný aktivní symbol.")
            return False
        
        current_price = self.get_current_price(target_symbol)
        if current_price is None:
            print(f"Nepodařilo se získat aktuální cenu pro {target_symbol}.")
            return False
        
        total_cost = quantity * current_price
        
        print(f"\n=== NÁKUPNÍ PŘÍKAZ ===")
        print(f"Symbol: {target_symbol}")
        print(f"Množství: {quantity}")
        print(f"Cena za jednotku: ${current_price:.2f}")
        print(f"Celková cena: ${total_cost:.2f}")
        print(f"Dostupná hotovost: ${self.portfolio.get_available_cash():.2f}")
        
        # Potvrzení od uživatele
        confirm = input("Potvrdit nákup? (y/n): ").lower().strip()
        if confirm != 'y':
            print("Nákup zrušen.")
            return False
        
        success = self.portfolio.add_position(target_symbol, quantity, current_price)
        
        if success:
            print(f"✅ Nákup úspěšně proveden!")
            print(f"Zbývající hotovost: ${self.portfolio.get_available_cash():.2f}")
        else:
            print("❌ Nákup se nezdařil.")
        
        return success
    
    def execute_sell_order(self, quantity: float, symbol: str = None) -> bool:
        """
        Provede prodejní příkaz
        
        Args:
            quantity: Množství k prodeji
            symbol: Symbol aktiva (pokud není zadán, použije se aktivní symbol)
            
        Returns:
            True pokud byl příkaz úspěšně proveden
        """
        target_symbol = symbol or self.current_symbol
        if not target_symbol:
            print("Není nastaven žádný aktivní symbol.")
            return False
        
        current_price = self.get_current_price(target_symbol)
        if current_price is None:
            print(f"Nepodařilo se získat aktuální cenu pro {target_symbol}.")
            return False
        
        position = self.portfolio.get_position(target_symbol)
        if not position or position.quantity < quantity:
            available = position.quantity if position else 0
            print(f"Nedostatek akcií pro prodej! Máte: {available}, chcete prodat: {quantity}")
            return False
        
        total_value = quantity * current_price
        
        print(f"\n=== PRODEJNÍ PŘÍKAZ ===")
        print(f"Symbol: {target_symbol}")
        print(f"Množství: {quantity}")
        print(f"Cena za jednotku: ${current_price:.2f}")
        print(f"Celková hodnota: ${total_value:.2f}")
        print(f"Dostupné množství: {position.quantity}")
        
        if position:
            pnl = quantity * (current_price - position.entry_price)
            pnl_percent = (pnl / (quantity * position.entry_price)) * 100
            print(f"Očekávaný zisk/ztráta: ${pnl:.2f} ({pnl_percent:.2f}%)")
        
        # Potvrzení od uživatele
        confirm = input("Potvrdit prodej? (y/n): ").lower().strip()
        if confirm != 'y':
            print("Prodej zrušen.")
            return False
        
        success = self.portfolio.add_position(target_symbol, -quantity, current_price)
        
        if success:
            print(f"✅ Prodej úspěšně proveden!")
            print(f"Nová hotovost: ${self.portfolio.get_available_cash():.2f}")
        else:
            print("❌ Prodej se nezdařil.")
        
        return success
    
    def show_portfolio_summary(self):
        """Zobrazí souhrn portfolia"""
        # Získáme aktuální ceny pro všechny pozice
        current_prices = {}
        for symbol in self.portfolio.positions.keys():
            price = self.market_data.get_current_price(symbol)
            if price is not None:
                current_prices[symbol] = price
        
        summary = self.portfolio.get_portfolio_summary(current_prices)
        
        print(f"\n{'='*50}")
        print(f"           SOUHRN PORTFOLIA")
        print(f"{'='*50}")
        print(f"Hotovost: ${summary['cash']:.2f}")
        print(f"Celková hodnota: ${summary['total_value']:.2f}")
        print(f"Celkový P&L: ${summary['total_pnl']:.2f} ({summary['total_pnl_percent']:.2f}%)")
        
        if summary['positions']:
            print(f"\n{'='*50}")
            print(f"              POZICE")
            print(f"{'='*50}")
            print(f"{'Symbol':<10} {'Množství':<10} {'Vstup':<10} {'Aktuální':<10} {'P&L':<15} {'P&L%':<10}")
            print(f"{'-'*70}")
            
            for pos in summary['positions']:
                pnl_color = "+" if pos['unrealized_pnl'] >= 0 else ""
                print(f"{pos['symbol']:<10} {pos['quantity']:<10.2f} "
                      f"${pos['entry_price']:<9.2f} ${pos['current_price']:<9.2f} "
                      f"{pnl_color}${pos['unrealized_pnl']:<14.2f} "
                      f"{pnl_color}{pos['unrealized_pnl_percent']:<9.2f}%")
        else:
            print("\nŽádné otevřené pozice.")
    
    def plot_price_chart(self, symbol: str = None, period: str = "1mo"):
        """
        Vykreslí cenový graf aktiva
        
        Args:
            symbol: Symbol aktiva (pokud není zadán, použije se aktivní symbol)
            period: Období pro graf (1d, 5d, 1mo, 3mo, 6mo, 1y)
        """
        target_symbol = symbol or self.current_symbol
        if not target_symbol:
            print("Není nastaven žádný aktivní symbol.")
            return
        
        print(f"Načítám data pro {target_symbol}...")
        hist_data = self.market_data.get_historical_data(target_symbol, period)
        
        if hist_data is None or hist_data.empty:
            print(f"Nepodařilo se načíst data pro {target_symbol}.")
            return
        
        # Vytvoření grafu
        plt.figure(figsize=(12, 8))
        
        # Hlavní cenový graf
        plt.subplot(2, 1, 1)
        plt.plot(hist_data.index, hist_data['Close'], linewidth=2, label='Zavírací cena')
        plt.title(f'{target_symbol} - Cenový graf ({period})', fontsize=16, fontweight='bold')
        plt.ylabel('Cena ($)', fontsize=12)
        plt.grid(True, alpha=0.3)
        plt.legend()
        
        # Formátování osy x pro datumy
        plt.gca().xaxis.set_major_formatter(mdates.DateFormatter('%Y-%m-%d'))
        plt.gca().xaxis.set_major_locator(mdates.WeekdayLocator(interval=1))
        plt.xticks(rotation=45)
        
        # Graf objemu
        plt.subplot(2, 1, 2)
        plt.bar(hist_data.index, hist_data['Volume'], alpha=0.7, color='orange')
        plt.title('Objem obchodování', fontsize=14)
        plt.ylabel('Objem', fontsize=12)
        plt.grid(True, alpha=0.3)
        
        # Formátování osy x pro datumy
        plt.gca().xaxis.set_major_formatter(mdates.DateFormatter('%Y-%m-%d'))
        plt.gca().xaxis.set_major_locator(mdates.WeekdayLocator(interval=1))
        plt.xticks(rotation=45)
        
        plt.tight_layout()
        plt.show()
        
        # Zobrazení základních statistik
        current_price = hist_data['Close'].iloc[-1]
        price_change = current_price - hist_data['Close'].iloc[0]
        price_change_percent = (price_change / hist_data['Close'].iloc[0]) * 100
        
        print(f"\n=== STATISTIKY PRO {target_symbol} ===")
        print(f"Aktuální cena: ${current_price:.2f}")
        print(f"Změna za období: ${price_change:.2f} ({price_change_percent:.2f}%)")
        print(f"Maximum: ${hist_data['High'].max():.2f}")
        print(f"Minimum: ${hist_data['Low'].min():.2f}")
        print(f"Průměrný objem: {hist_data['Volume'].mean():,.0f}")
    
    def save_portfolio(self, filename: str = "portfolio.json"):
        """Uloží portfolio do souboru"""
        self.portfolio.save_to_file(filename)
        print(f"Portfolio uloženo do {filename}")
    
    def load_portfolio(self, filename: str = "portfolio.json"):
        """Načte portfolio ze souboru"""
        if self.portfolio.load_from_file(filename):
            print(f"Portfolio načteno ze souboru {filename}")
        else:
            print("Portfolio se nepodařilo načíst, začínám s novým.")
