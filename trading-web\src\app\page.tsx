'use client'

import { useState, useEffect } from 'react'
import { useTradingStore } from '@/lib/store'
import { tradingAPI } from '@/lib/api'
import { POPULAR_SYMBOLS } from '@/types/trading'

// Import komponent (vytvoř<PERSON>me je pozd<PERSON>)
import Header from '@/components/Header'
import AssetSearch from '@/components/AssetSearch'
import AssetInfo from '@/components/AssetInfo'
import TradingChart from '@/components/TradingChart'
import TradingPanel from '@/components/TradingPanel'
import Portfolio from '@/components/Portfolio'
import Watchlist from '@/components/Watchlist'
import MarketOverview from '@/components/MarketOverview'

export default function TradingApp() {
  const {
    selectedAsset,
    portfolio,
    watchlist,
    isLoading,
    error,
    setSelectedAsset,
    setLoading,
    setError,
    updatePositionPrices,
  } = useTradingStore()

  const [selectedPeriod, setSelectedPeriod] = useState('1M')

  // Načtení výchozího aktiva při startu
  useEffect(() => {
    const loadDefaultAsset = async () => {
      if (!selectedAsset) {
        setLoading(true)
        try {
          const response = await tradingAPI.getAssetInfo('AAPL')
          if (response.success && response.data) {
            setSelectedAsset(response.data)
          }
        } catch (error) {
          setError('Nepodařilo se načíst výchozí aktivum')
        } finally {
          setLoading(false)
        }
      }
    }

    loadDefaultAsset()
  }, [selectedAsset, setSelectedAsset, setLoading, setError])

  // Aktualizace cen pozic každých 30 sekund
  useEffect(() => {
    const updatePrices = async () => {
      if (!portfolio?.positions.length) return

      const symbols = portfolio.positions.map(p => p.symbol)
      try {
        const response = await tradingAPI.getMultiplePrices(symbols)
        if (response.success && response.data) {
          updatePositionPrices(response.data)
        }
      } catch (error) {
        console.error('Chyba při aktualizaci cen:', error)
      }
    }

    // Okamžitá aktualizace
    updatePrices()

    // Pravidelná aktualizace každých 30 sekund
    const interval = setInterval(updatePrices, 30000)

    return () => clearInterval(interval)
  }, [portfolio?.positions, updatePositionPrices])

  if (isLoading && !selectedAsset) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="loading-pulse w-16 h-16 mx-auto mb-4"></div>
          <p className="text-muted-foreground">Načítání aplikace...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-background">
      <Header />
      
      {error && (
        <div className="bg-destructive/10 border border-destructive/20 text-destructive px-4 py-3 mx-4 mt-4 rounded-md">
          <p className="text-sm">{error}</p>
          <button 
            onClick={() => setError(null)}
            className="text-xs underline mt-1"
          >
            Zavřít
          </button>
        </div>
      )}

      <main className="container mx-auto px-4 py-6">
        {/* Vyhledávání aktiv */}
        <div className="mb-6">
          <AssetSearch />
        </div>

        {/* Hlavní obsah */}
        <div className="trading-grid">
          {/* Levý panel - Portfolio a Watchlist */}
          <div className="space-y-6">
            <Portfolio />
            <Watchlist />
          </div>

          {/* Střední panel - Graf a informace o aktivu */}
          <div className="space-y-6">
            {selectedAsset && (
              <>
                <AssetInfo asset={selectedAsset} />
                <TradingChart 
                  symbol={selectedAsset.symbol} 
                  period={selectedPeriod}
                  onPeriodChange={setSelectedPeriod}
                />
              </>
            )}
          </div>

          {/* Pravý panel - Trading panel a přehled trhu */}
          <div className="space-y-6">
            {selectedAsset && (
              <TradingPanel asset={selectedAsset} />
            )}
            <MarketOverview />
          </div>
        </div>
      </main>
    </div>
  )
}
